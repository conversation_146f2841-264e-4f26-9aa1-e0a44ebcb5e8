/**
 * Performance Monitoring and Optimization Utilities
 * Provides tools for measuring and optimizing application performance
 */

// ===== PERFORMANCE MEASUREMENT =====

interface PerformanceMetrics {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  /**
   * Start measuring performance for a specific operation
   */
  start(name: string, metadata?: Record<string, any>): void {
    const startTime = performance.now();
    this.metrics.set(name, {
      name,
      startTime,
      metadata
    });

    // Use Performance API mark if available
    if (typeof performance.mark === 'function') {
      performance.mark(`${name}-start`);
    }
  }

  /**
   * End measuring performance for a specific operation
   */
  end(name: string): PerformanceMetrics | null {
    const endTime = performance.now();
    const metric = this.metrics.get(name);

    if (!metric) {
      console.warn(`Performance metric "${name}" was not started`);
      return null;
    }

    const duration = endTime - metric.startTime;
    const completedMetric: PerformanceMetrics = {
      ...metric,
      endTime,
      duration
    };

    this.metrics.set(name, completedMetric);

    // Use Performance API mark and measure if available
    if (typeof performance.mark === 'function' && typeof performance.measure === 'function') {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
    }

    // Log performance in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`, completedMetric);
    }

    return completedMetric;
  }

  /**
   * Get all metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Get specific metric
   */
  getMetric(name: string): PerformanceMetrics | undefined {
    return this.metrics.get(name);
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
    if (typeof performance.clearMarks === 'function') {
      performance.clearMarks();
    }
    if (typeof performance.clearMeasures === 'function') {
      performance.clearMeasures();
    }
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    if (typeof PerformanceObserver === 'undefined') return;

    try {
      // Observe navigation timing
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            this.handleNavigationTiming(entry as PerformanceNavigationTiming);
          }
        });
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Observe resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            this.handleResourceTiming(entry as PerformanceResourceTiming);
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // Observe paint timing
      const paintObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'paint') {
            this.handlePaintTiming(entry);
          }
        });
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);

    } catch (error) {
      console.warn('Failed to initialize performance observers:', error);
    }
  }

  /**
   * Handle navigation timing
   */
  private handleNavigationTiming(entry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      domInteractive: entry.domInteractive - entry.navigationStart,
      firstByte: entry.responseStart - entry.navigationStart,
      dnsLookup: entry.domainLookupEnd - entry.domainLookupStart,
      tcpConnect: entry.connectEnd - entry.connectStart,
      request: entry.responseEnd - entry.requestStart,
      response: entry.responseEnd - entry.responseStart,
      domProcessing: entry.domComplete - entry.domLoading
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('Navigation Timing:', metrics);
    }

    // Report to analytics service if available
    this.reportToAnalytics('navigation', metrics);
  }

  /**
   * Handle resource timing
   */
  private handleResourceTiming(entry: PerformanceResourceTiming): void {
    // Only log slow resources in development
    if (process.env.NODE_ENV === 'development' && entry.duration > 1000) {
      console.warn(`Slow resource: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
    }

    // Report slow resources to analytics
    if (entry.duration > 2000) {
      this.reportToAnalytics('slow-resource', {
        name: entry.name,
        duration: entry.duration,
        size: entry.transferSize
      });
    }
  }

  /**
   * Handle paint timing
   */
  private handlePaintTiming(entry: PerformanceEntry): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`${entry.name}: ${entry.startTime.toFixed(2)}ms`);
    }

    this.reportToAnalytics('paint', {
      name: entry.name,
      startTime: entry.startTime
    });
  }

  /**
   * Report metrics to analytics service
   */
  private reportToAnalytics(type: string, data: any): void {
    // In a real application, you would send this to your analytics service
    // Example: Google Analytics, DataDog, New Relic, etc.
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'performance_metric', {
        event_category: 'Performance',
        event_label: type,
        value: Math.round(data.duration || data.startTime || 0),
        custom_map: { metric_data: JSON.stringify(data) }
      });
    }
  }

  /**
   * Cleanup observers
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.clear();
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// ===== PERFORMANCE HOOKS =====

import { useEffect, useRef, useState } from 'react';

/**
 * Hook to measure component render performance
 */
export function useRenderPerformance(componentName: string) {
  const renderCount = useRef(0);
  const startTime = useRef<number>(0);

  useEffect(() => {
    renderCount.current++;
    startTime.current = performance.now();
    
    return () => {
      const duration = performance.now() - startTime.current;
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render #${renderCount.current} took ${duration.toFixed(2)}ms`);
      }
    };
  });

  return renderCount.current;
}

/**
 * Hook to measure API call performance
 */
export function useApiPerformance() {
  const [metrics, setMetrics] = useState<Record<string, number>>({});

  const measureApiCall = async <T>(
    name: string,
    apiCall: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await apiCall();
      const duration = performance.now() - startTime;
      
      setMetrics(prev => ({ ...prev, [name]: duration }));
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`API call ${name} took ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      setMetrics(prev => ({ ...prev, [`${name}_error`]: duration }));
      throw error;
    }
  };

  return { metrics, measureApiCall };
}

/**
 * Hook to measure page load performance
 */
export function usePageLoadPerformance(pageName: string) {
  useEffect(() => {
    performanceMonitor.start(`page-load-${pageName}`);
    
    const handleLoad = () => {
      performanceMonitor.end(`page-load-${pageName}`);
    };

    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);
      return () => window.removeEventListener('load', handleLoad);
    }
  }, [pageName]);
}

// ===== PERFORMANCE UTILITIES =====

/**
 * Debounce function to limit function calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit function calls
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Lazy load component with performance tracking
 */
export function lazyLoadWithPerformance<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  componentName: string
) {
  return React.lazy(async () => {
    performanceMonitor.start(`lazy-load-${componentName}`);
    
    try {
      const module = await importFunc();
      performanceMonitor.end(`lazy-load-${componentName}`);
      return module;
    } catch (error) {
      performanceMonitor.end(`lazy-load-${componentName}`);
      throw error;
    }
  });
}

/**
 * Measure function execution time
 */
export function measureExecution<T extends (...args: any[]) => any>(
  func: T,
  name: string
): T {
  return ((...args: Parameters<T>) => {
    performanceMonitor.start(name);
    
    try {
      const result = func(...args);
      
      if (result instanceof Promise) {
        return result.finally(() => {
          performanceMonitor.end(name);
        });
      } else {
        performanceMonitor.end(name);
        return result;
      }
    } catch (error) {
      performanceMonitor.end(name);
      throw error;
    }
  }) as T;
}

/**
 * Get Core Web Vitals
 */
export function getCoreWebVitals(): Promise<{
  fcp?: number;
  lcp?: number;
  fid?: number;
  cls?: number;
}> {
  return new Promise((resolve) => {
    const vitals: any = {};
    
    // First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          vitals.fcp = entry.startTime;
        }
      });
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      vitals.lcp = lastEntry.startTime;
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        vitals.fid = entry.processingStart - entry.startTime;
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      vitals.cls = clsValue;
    }).observe({ entryTypes: ['layout-shift'] });

    // Resolve after a delay to collect metrics
    setTimeout(() => resolve(vitals), 3000);
  });
}

// Export performance monitor for global use
export default performanceMonitor;
