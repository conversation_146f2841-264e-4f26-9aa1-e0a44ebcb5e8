/**
 * Error Boundary Components
 * Provides comprehensive error handling and recovery mechanisms
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './Card';
import { Button } from './Button';
import { announceToScreenReader } from '@/utils/accessibility';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'section' | 'component';
}

/**
 * Main Error Boundary Component
 * Catches JavaScript errors anywhere in the child component tree
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Announce error to screen readers
    announceToScreenReader(
      `An error occurred: ${error.message}. Please try refreshing the page.`,
      'assertive',
    );

    // Report error to monitoring service (if available)
    this.reportError(error, errorInfo);
  }

  private reportError(error: Error, errorInfo: ErrorInfo) {
    // In a real application, you would send this to your error reporting service
    // Example: Sentry, LogRocket, Bugsnag, etc.
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId,
    };

    // For now, just log to console
    console.error('Error Report:', errorReport);

    // In production, you would do something like:
    // errorReportingService.captureException(error, { extra: errorReport });
  }

  private handleRetry = () => {
    // Reset error state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });

    // Announce retry to screen readers
    announceToScreenReader('Retrying...', 'polite');
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI based on level
      return this.renderErrorUI();
    }

    return this.props.children;
  }

  private renderErrorUI() {
    const { level = 'component', showDetails = false } = this.props;
    const { error, errorInfo, errorId } = this.state;

    switch (level) {
      case 'page':
        return (
          <PageErrorFallback
            error={error}
            errorInfo={errorInfo}
            errorId={errorId}
            onRetry={this.handleRetry}
            onReload={this.handleReload}
            onGoHome={this.handleGoHome}
            showDetails={showDetails}
          />
        );

      case 'section':
        return (
          <SectionErrorFallback
            error={error}
            errorInfo={errorInfo}
            errorId={errorId}
            onRetry={this.handleRetry}
            showDetails={showDetails}
          />
        );

      default:
        return (
          <ComponentErrorFallback
            error={error}
            errorInfo={errorInfo}
            errorId={errorId}
            onRetry={this.handleRetry}
            showDetails={showDetails}
          />
        );
    }
  }
}

// ===== ERROR FALLBACK COMPONENTS =====

interface ErrorFallbackProps {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  onRetry: () => void;
  showDetails?: boolean;
}

interface PageErrorFallbackProps extends ErrorFallbackProps {
  onReload: () => void;
  onGoHome: () => void;
}

function PageErrorFallback({
  error,
  errorInfo,
  errorId,
  onRetry,
  onReload,
  onGoHome,
  showDetails = false,
}: PageErrorFallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full">
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="text-center">
            <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" aria-hidden="true" />
            <CardTitle className="text-red-800">Something went wrong</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-red-700 text-center">
              We're sorry, but something unexpected happened. Please try one of the options below.
            </p>

            <div className="space-y-2">
              <Button onClick={onRetry} className="w-full" variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button onClick={onReload} className="w-full" variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
              <Button onClick={onGoHome} className="w-full">
                <Home className="h-4 w-4 mr-2" />
                Go to Dashboard
              </Button>
            </div>

            {showDetails && error && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm font-medium text-red-800 mb-2">
                  <Bug className="h-4 w-4 inline mr-1" />
                  Technical Details
                </summary>
                <div className="bg-red-100 p-3 rounded text-xs font-mono text-red-800 overflow-auto max-h-32">
                  <p>
                    <strong>Error ID:</strong> {errorId}
                  </p>
                  <p>
                    <strong>Message:</strong> {error.message}
                  </p>
                  {error.stack && (
                    <p>
                      <strong>Stack:</strong> {error.stack}
                    </p>
                  )}
                </div>
              </details>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function SectionErrorFallback({
  error,
  errorInfo,
  errorId,
  onRetry,
  showDetails = false,
}: ErrorFallbackProps) {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardContent className="p-6">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" aria-hidden="true" />
          <h3 className="text-lg font-semibold text-red-800 mb-2">Section Error</h3>
          <p className="text-red-700 mb-4">
            This section encountered an error and couldn't load properly.
          </p>
          <Button onClick={onRetry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>

          {showDetails && error && (
            <details className="mt-4 text-left">
              <summary className="cursor-pointer text-sm font-medium text-red-800 mb-2">
                Technical Details
              </summary>
              <div className="bg-red-100 p-3 rounded text-xs font-mono text-red-800 overflow-auto max-h-24">
                <p>
                  <strong>Error ID:</strong> {errorId}
                </p>
                <p>
                  <strong>Message:</strong> {error.message}
                </p>
              </div>
            </details>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function ComponentErrorFallback({
  error,
  errorInfo,
  errorId,
  onRetry,
  showDetails = false,
}: ErrorFallbackProps) {
  return (
    <div className="border border-red-200 bg-red-50 rounded-lg p-4">
      <div className="flex items-center gap-3">
        <AlertTriangle className="h-6 w-6 text-red-500 flex-shrink-0" aria-hidden="true" />
        <div className="flex-1">
          <p className="text-sm font-medium text-red-800">Component Error</p>
          <p className="text-xs text-red-700">This component failed to load.</p>
        </div>
        <Button onClick={onRetry} size="sm" variant="outline">
          <RefreshCw className="h-3 w-3" />
        </Button>
      </div>

      {showDetails && error && (
        <details className="mt-3">
          <summary className="cursor-pointer text-xs font-medium text-red-800">Details</summary>
          <div className="bg-red-100 p-2 rounded text-xs font-mono text-red-800 mt-1 overflow-auto max-h-16">
            <p>
              <strong>ID:</strong> {errorId}
            </p>
            <p>
              <strong>Error:</strong> {error.message}
            </p>
          </div>
        </details>
      )}
    </div>
  );
}

// ===== SPECIALIZED ERROR BOUNDARIES =====

/**
 * Dashboard Error Boundary
 * Specialized for dashboard components
 */
export function DashboardErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      level="section"
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        // Custom dashboard error handling
        console.error('Dashboard Error:', error, errorInfo);
      }}
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * Chart Error Boundary
 * Specialized for chart components
 */
export function ChartErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      level="component"
      fallback={
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-center">
            <AlertTriangle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Chart failed to load</p>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * Form Error Boundary
 * Specialized for form components
 */
export function FormErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      level="component"
      fallback={
        <div className="border border-red-200 bg-red-50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <p className="text-sm text-red-800">Form component error</p>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
}

// ===== HOOKS FOR ERROR HANDLING =====

/**
 * Hook for handling async errors
 */
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
    console.error('Async Error:', error);

    // Announce error to screen readers
    announceToScreenReader(`An error occurred: ${error.message}`, 'assertive');
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
}

// Export all components
export default ErrorBoundary;
