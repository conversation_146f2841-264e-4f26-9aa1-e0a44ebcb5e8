import Navbar from '../components/layout/Navbar'; // Import Navbar

/**
 * Home page for the application.
 * @returns {JSX.Element} The home page component.
 */
export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen" style={{ backgroundColor: 'var(--color-background, #F5F5F5)', color: 'var(--color-text-primary, #333333)' }}>
      <Navbar />
      <main className="flex flex-grow flex-col items-center justify-center p-24">
        <h1
          className="text-4xl font-bold text-center"
          style={{ color: 'var(--color-text-primary, #333333)' }}
        >
          Welcome to Comply Checker
        </h1>
        <p
          className="mt-4 text-lg text-center"
          style={{ color: 'var(--color-text-secondary, #666666)' }}
        >
          Your solution for automated compliance scanning.
        </p>
        {/* Placeholder for future content, e.g., scan submission form */}
      </main>
      <footer
        className="w-full p-4 text-center"
        style={{
          backgroundColor: 'var(--color-text-primary, #333333)',
          color: 'var(--color-background-secondary, white)'
        }}
      >
        © {new Date().getFullYear()} Comply Checker - All Rights Reserved
      </footer>
    </div>
  );
}
