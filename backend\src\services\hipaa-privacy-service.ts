import { HipaaPrivacyPolicyOrchestrator } from '../compliance/hipaa/privacy/services/privacy-orchestrator';
import { HipaaScanResult, HipaaScanOptions } from '../compliance/hipaa/privacy/types';

/**
 * HIPAA Privacy Service
 * Wrapper service for HIPAA privacy compliance operations
 */
export class HipaaPrivacyService {
  private orchestrator: HipaaPrivacyPolicyOrchestrator;

  constructor() {
    this.orchestrator = new HipaaPrivacyPolicyOrchestrator();
  }

  /**
   * Get recent privacy scans from database
   */
  async getRecentScans(limit: number = 50): Promise<HipaaScanResult[]> {
    try {
      console.log('📋 [Privacy Service] Fetching recent privacy scans from database...');
      console.log('🔍 [Privacy Service] Query parameters:', { limit });

      // Import database utilities
      const { HipaaDatabase } = await import('../compliance/hipaa/privacy/database/hipaa-privacy-db');
      const knex = (await import('../lib/db')).default;

      // Get recent scans from the database
      const scans = await knex('hipaa_scans')
        .join('scans', 'hipaa_scans.scan_id', 'scans.id')
        .select(
          'hipaa_scans.id',
          'hipaa_scans.target_url as targetUrl',
          'hipaa_scans.overall_score as overallScore',
          'hipaa_scans.overall_passed as overallPassed',
          'hipaa_scans.compliance_level as complianceLevel',
          'hipaa_scans.total_checks as totalChecks',
          'hipaa_scans.passed_checks as passedChecks',
          'hipaa_scans.failed_checks as failedChecks',
          'hipaa_scans.critical_issues as criticalIssues',
          'hipaa_scans.high_issues as highIssues',
          'hipaa_scans.medium_issues as mediumIssues',
          'hipaa_scans.low_issues as lowIssues',
          'hipaa_scans.created_at as timestamp',
          'scans.status'
        )
        .orderBy('hipaa_scans.created_at', 'desc')
        .limit(limit);

      console.log(`✅ [Privacy Service] Found ${scans.length} privacy scans in database`);

      // Helper function to calculate risk level
      const calculateRiskLevel = (
        criticalIssues: number,
        highIssues: number,
        mediumIssues: number,
        _lowIssues: number,
      ): 'critical' | 'high' | 'medium' | 'low' => {
        if (criticalIssues > 0) return 'critical';
        if (highIssues > 0) return 'high';
        if (mediumIssues > 0) return 'medium';
        return 'low';
      };

      // Transform database results to HipaaScanResult format
      const transformedScans: HipaaScanResult[] = scans.map(scan => ({
        targetUrl: scan.targetUrl,
        timestamp: scan.timestamp,
        overallScore: scan.overallScore,
        overallPassed: scan.overallPassed,
        summary: {
          totalChecks: scan.totalChecks,
          passedChecks: scan.passedChecks,
          failedChecks: scan.failedChecks,
          criticalIssues: scan.criticalIssues,
          highIssues: scan.highIssues,
          mediumIssues: scan.mediumIssues,
          lowIssues: scan.lowIssues,
          overallScore: scan.overallScore,
          complianceLevel: scan.complianceLevel,
          riskLevel: calculateRiskLevel(scan.criticalIssues, scan.highIssues, scan.mediumIssues, scan.lowIssues),
          analysisLevelsUsed: [1, 2, 3], // Default for now
        },
        checks: [], // Detailed checks not needed for recent scans list
        recommendations: [], // Detailed recommendations not needed for recent scans list
        metadata: {
          version: '1.0.0',
          processingTime: 0, // Not stored in summary table
          checksPerformed: scan.totalChecks,
          analysisLevelsUsed: [1, 2, 3],
          cacheHits: 0,
          errors: [],
          warnings: [],
          userAgent: 'HIPAA-Compliance-Scanner/1.0',
          scanOptions: {
            timeout: 30000,
            maxRedirects: 5,
            userAgent: 'HIPAA-Compliance-Scanner/1.0',
          },
        },
      }));

      console.log(`🔄 [Privacy Service] Transformed ${transformedScans.length} scans for dashboard`);
      return transformedScans;
    } catch (error) {
      console.error('❌ [Privacy Service] Error fetching privacy scans:', error);
      return [];
    }
  }

  /**
   * Perform a new privacy scan
   */
  async performScan(targetUrl: string, options: HipaaScanOptions = {}): Promise<HipaaScanResult> {
    try {
      return await this.orchestrator.performComprehensiveScan(targetUrl, options);
    } catch (error) {
      console.error('Error performing privacy scan:', error);
      throw error;
    }
  }

  /**
   * Get scan by ID
   * TODO: Implement database query to get specific scan
   */
  async getScanById(scanId: string): Promise<HipaaScanResult | null> {
    try {
      // TODO: Replace with actual database query
      console.log(`Getting privacy scan by ID: ${scanId}`);
      return null;
    } catch (error) {
      console.error('Error fetching privacy scan by ID:', error);
      return null;
    }
  }

  /**
   * Get scan statistics
   */
  async getStatistics(): Promise<{
    totalScans: number;
    averageScore: number;
    lastScanDate: string | null;
  }> {
    try {
      const recentScans = await this.getRecentScans();

      if (recentScans.length === 0) {
        return {
          totalScans: 0,
          averageScore: 0,
          lastScanDate: null,
        };
      }

      const totalScans = recentScans.length;
      const averageScore = Math.round(
        recentScans.reduce((sum, scan) => sum + scan.overallScore, 0) / totalScans,
      );
      const lastScanDate = recentScans.sort(
        (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      )[0].timestamp;

      return {
        totalScans,
        averageScore,
        lastScanDate,
      };
    } catch (error) {
      console.error('Error getting privacy scan statistics:', error);
      return {
        totalScans: 0,
        averageScore: 0,
        lastScanDate: null,
      };
    }
  }
}
