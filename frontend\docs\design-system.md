# Comply Checker Design System

## Overview
This design system ensures WCAG AA compliance and consistent visual hierarchy across the Comply Checker application. All components must adhere to these standards to maintain accessibility and professional appearance.

## Color Palette

### Primary Colors
- **Primary Blue**: `#0055A4`
  - Use for: Primary buttons, primary actions, key navigation elements
  - Contrast ratio: 4.5:1 with white text (WCAG AA compliant)
  
- **Accent Purple**: `#663399`
  - Use for: Secondary actions, accent elements, icons, highlights
  - Contrast ratio: 4.5:1 with white text (WCAG AA compliant)

### Background Colors
- **Light Gray Background**: `#F5F5F5`
  - Use for: Page backgrounds, section backgrounds
  
- **White**: `#FFFFFF`
  - Use for: Card backgrounds, content areas, button backgrounds

### Text Colors
- **Dark Gray**: `#333333`
  - Use for: Primary text, headings, labels
  - Contrast ratio: 12.6:1 with white background (WCAG AAA compliant)
  
- **Medium Gray**: `#666666`
  - Use for: Secondary text, descriptions, metadata
  - Contrast ratio: 7:1 with white background (WCAG AAA compliant)

### Status Colors
- **Success Green**: `#22C55E`
- **Warning Orange**: `#F59E0B`
- **Error Red**: `#EF4444`
- **Info Blue**: `#3B82F6`

## Button Standards

### Primary Button
```tsx
<Button 
  style={{ 
    backgroundColor: '#0055A4', 
    color: 'white',
    border: 'none'
  }}
  className="hover:bg-blue-700 transition-colors shadow-md"
>
  Primary Action
</Button>
```

### Secondary Button (Outline)
```tsx
<Button 
  variant="outline"
  style={{ 
    borderColor: '#0055A4', 
    color: '#0055A4',
    backgroundColor: 'white'
  }}
  className="hover:bg-blue-50 transition-colors"
>
  Secondary Action
</Button>
```

### Accent Button (Purple)
```tsx
<Button 
  variant="outline"
  style={{ 
    borderColor: '#663399', 
    color: '#663399',
    backgroundColor: 'white'
  }}
  className="hover:bg-purple-50 transition-colors"
>
  Accent Action
</Button>
```

## Interactive States

### Hover Effects
- **Primary buttons**: Darken background by 10% (`hover:bg-blue-700`)
- **Outline buttons**: Light background tint (`hover:bg-blue-50`, `hover:bg-purple-50`)
- **All buttons**: Include `transition-colors` for smooth animations

### Focus States
- Use browser default focus rings or custom focus styles with 2px outline
- Ensure focus indicators have 3:1 contrast ratio with background

### Active States
- Slightly darken hover state colors
- Add subtle shadow or border changes

## Typography Hierarchy

### Headings
```tsx
// H1 - Page titles
<h1 className="text-3xl font-bold" style={{ color: '#333333' }}>
  Page Title
</h1>

// H2 - Section titles
<h2 className="text-2xl font-semibold" style={{ color: '#333333' }}>
  Section Title
</h2>

// H3 - Subsection titles
<h3 className="text-xl font-semibold" style={{ color: '#333333' }}>
  Subsection Title
</h3>
```

### Body Text
```tsx
// Primary text
<p style={{ color: '#333333' }}>Primary content text</p>

// Secondary text
<p style={{ color: '#666666' }}>Secondary or metadata text</p>
```

## Layout Standards

### Page Structure
```tsx
<div 
  className="min-h-screen"
  style={{ backgroundColor: '#F5F5F5', color: '#333333' }}
>
  <div className="container mx-auto p-6 space-y-6">
    {/* Page content */}
  </div>
</div>
```

### Card Components
```tsx
<Card style={{ backgroundColor: 'white', color: '#333333' }}>
  <CardHeader>
    <CardTitle style={{ color: '#333333' }}>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    {/* Card content */}
  </CardContent>
</Card>
```

## Spacing System

### Standard Spacing
- **xs**: `0.25rem` (4px)
- **sm**: `0.5rem` (8px)
- **md**: `1rem` (16px)
- **lg**: `1.5rem` (24px)
- **xl**: `2rem` (32px)
- **2xl**: `3rem` (48px)

### Component Spacing
- **Button padding**: `p-4` for large buttons, `p-2` for small buttons
- **Card padding**: `p-6` for content, `p-12` for empty states
- **Section spacing**: `space-y-6` between major sections

## Icon Standards

### Icon Colors
```tsx
// Primary icons (navigation, main actions)
<Icon style={{ color: '#0055A4' }} />

// Accent icons (secondary actions, highlights)
<Icon style={{ color: '#663399' }} />

// Status icons
<Icon style={{ color: '#22C55E' }} /> // Success
<Icon style={{ color: '#F59E0B' }} /> // Warning
<Icon style={{ color: '#EF4444' }} /> // Error
```

### Icon Sizes
- **Small**: `h-4 w-4` (16px)
- **Medium**: `h-6 w-6` (24px)
- **Large**: `h-8 w-8` (32px)
- **Extra Large**: `h-16 w-16` (64px) - for empty states

## Component-Specific Guidelines

### Navigation Elements
- Use Primary Blue (`#0055A4`) for active/current page indicators
- Use outline style for secondary navigation items
- Include hover states with light background tints

### Form Elements
- Input borders: `#D1D5DB` (neutral gray)
- Focus borders: `#0055A4` (primary blue)
- Error borders: `#EF4444` (error red)
- Label text: `#333333` (dark gray)

### Status Indicators
- **Compliant/Success**: Green background with white text
- **Non-compliant/Error**: Red background with white text
- **Warning**: Orange background with white text
- **In Progress**: Blue background with white text

## Accessibility Requirements

### Contrast Ratios
- **Normal text**: Minimum 4.5:1 (WCAG AA)
- **Large text**: Minimum 3:1 (WCAG AA)
- **UI components**: Minimum 3:1 for borders and focus indicators

### Interactive Elements
- Minimum touch target size: 44x44px
- Clear focus indicators for keyboard navigation
- Descriptive alt text for images and icons
- Proper heading hierarchy (h1 → h2 → h3)

### Motion and Animation
- Use `transition-colors` for smooth color changes
- Keep animations under 0.3 seconds
- Respect `prefers-reduced-motion` for accessibility

## Implementation Checklist

### Before Adding New Components
- [ ] Colors match the defined palette
- [ ] Contrast ratios meet WCAG AA standards
- [ ] Hover and focus states are defined
- [ ] Typography follows the hierarchy
- [ ] Spacing uses the standard system
- [ ] Interactive elements have proper touch targets

### Code Review Checklist
- [ ] No hardcoded colors outside the design system
- [ ] Consistent button styling across components
- [ ] Proper semantic HTML structure
- [ ] Accessible color combinations
- [ ] Smooth transitions and animations

## Common Patterns

### Empty States
```tsx
<Card style={{ backgroundColor: 'white', color: '#333333' }}>
  <CardContent className="p-12">
    <div className="text-center">
      <Icon className="h-16 w-16 mx-auto mb-4" style={{ color: '#663399' }} />
      <h3 className="text-xl font-semibold mb-2" style={{ color: '#333333' }}>
        Empty State Title
      </h3>
      <p className="mb-6 max-w-md mx-auto" style={{ color: '#666666' }}>
        Description text explaining the empty state.
      </p>
      <Button 
        style={{ backgroundColor: '#0055A4', color: 'white' }}
        className="hover:bg-blue-700 transition-colors"
      >
        Primary Action
      </Button>
    </div>
  </CardContent>
</Card>
```

### Action Sections
```tsx
<Card style={{ backgroundColor: 'white', color: '#333333' }}>
  <CardHeader>
    <CardTitle style={{ color: '#333333' }}>Quick Actions</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Primary action */}
      <Button 
        className="h-auto p-4 flex-col gap-2 hover:bg-blue-700 transition-colors"
        style={{ backgroundColor: '#0055A4', color: 'white' }}
      >
        <Icon className="h-6 w-6" />
        <span>Primary Action</span>
      </Button>
      
      {/* Secondary actions */}
      <Button 
        variant="outline"
        className="h-auto p-4 flex-col gap-2 hover:bg-blue-50 transition-colors"
        style={{ borderColor: '#0055A4', color: '#0055A4', backgroundColor: 'white' }}
      >
        <Icon className="h-6 w-6" />
        <span>Secondary Action</span>
      </Button>
    </div>
  </CardContent>
</Card>
```

## Responsive Design Guidelines

### Mobile-First Approach
- Design for mobile screens first, then enhance for larger screens
- Use responsive grid systems: `grid-cols-1 md:grid-cols-3`
- Ensure touch targets are minimum 44x44px on mobile devices

### Breakpoint Strategy
- **Mobile**: `< 768px` - Single column layouts, full-width buttons
- **Tablet**: `768px - 1024px` - Two-column layouts where appropriate
- **Desktop**: `> 1024px` - Multi-column layouts, compact spacing

### Mobile-Specific Adjustments
```tsx
// Button sizing
size={isMobile ? 'default' : 'sm'}

// Layout adjustments
className={`flex ${isMobile ? 'flex-col w-full' : 'flex-row'} gap-2`}

// Full-width on mobile
className={`${isMobile ? 'w-full justify-center' : ''}`}
```

## Error Handling & Feedback

### Error States
```tsx
<Card className="border-red-200" style={{ backgroundColor: '#FEF2F2' }}>
  <CardContent className="p-6">
    <div className="flex items-center gap-3" style={{ color: '#DC2626' }}>
      <AlertTriangle className="h-6 w-6" />
      <div>
        <h3 className="font-semibold">Error Title</h3>
        <p>Error description with actionable guidance.</p>
      </div>
    </div>
  </CardContent>
</Card>
```

### Loading States
```tsx
// Skeleton loading
<div className="space-y-3">
  <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
  <div className="h-6 bg-gray-200 rounded w-3/4 animate-pulse"></div>
</div>

// Button loading
<Button disabled={loading}>
  {loading ? (
    <>
      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
      Loading...
    </>
  ) : (
    'Normal State'
  )}
</Button>
```

### Success Feedback
```tsx
<Card className="border-green-200" style={{ backgroundColor: '#F0FDF4' }}>
  <CardContent className="p-4">
    <div className="flex items-center gap-2" style={{ color: '#16A34A' }}>
      <CheckCircle className="h-5 w-5" />
      <span>Success message</span>
    </div>
  </CardContent>
</Card>
```

## Performance Considerations

### Optimization Guidelines
- Use `transition-colors` instead of `transition-all` for better performance
- Implement proper loading states to prevent layout shifts
- Optimize images and icons for web delivery
- Use semantic HTML for better accessibility and SEO

### Animation Performance
```tsx
// Preferred: Specific property transitions
className="hover:bg-blue-50 transition-colors"

// Avoid: Generic transitions that affect all properties
className="hover:bg-blue-50 transition-all"
```

## Testing Guidelines

### Visual Testing Checklist
- [ ] All text meets WCAG AA contrast requirements (4.5:1 minimum)
- [ ] Interactive elements have clear hover and focus states
- [ ] Components work across different screen sizes
- [ ] Color combinations are accessible to colorblind users
- [ ] Loading and error states display correctly

### Accessibility Testing
- [ ] Keyboard navigation works for all interactive elements
- [ ] Screen readers can access all content and functionality
- [ ] Focus indicators are visible and meet contrast requirements
- [ ] Alternative text is provided for images and icons
- [ ] Proper heading hierarchy is maintained

### Browser Testing
- Test on major browsers: Chrome, Firefox, Safari, Edge
- Verify responsive behavior on different devices
- Check for consistent rendering across platforms

## Migration Guide

### Updating Existing Components
1. **Identify non-compliant elements**: Look for hardcoded colors, missing hover states
2. **Apply design system colors**: Replace with standardized color palette
3. **Add interactive states**: Ensure hover, focus, and active states are defined
4. **Test accessibility**: Verify contrast ratios and keyboard navigation
5. **Update documentation**: Document any component-specific variations

### Common Migration Patterns
```tsx
// Before: Hardcoded styling
<Button className="bg-black text-white">Action</Button>

// After: Design system compliant
<Button
  style={{ backgroundColor: '#0055A4', color: 'white' }}
  className="hover:bg-blue-700 transition-colors"
>
  Action
</Button>
```

## Maintenance & Updates

### Regular Review Process
- **Monthly**: Review new components for design system compliance
- **Quarterly**: Audit existing components for consistency
- **Annually**: Update color palette and accessibility standards as needed

### Documentation Updates
- Update this document when new patterns are established
- Include examples for complex component combinations
- Maintain version history for breaking changes

### Team Guidelines
- All new components must follow this design system
- Code reviews should include design system compliance checks
- Designers and developers should collaborate on new patterns

---

**Last Updated**: December 2024
**Version**: 1.0
**Maintained by**: Comply Checker Development Team

## Dark Mode Implementation

### Custom Dark Mode Theme
The application includes a custom dark mode implementation that maintains WCAG AA compliance while integrating with the established design system colors.

#### Dark Mode Color Palette
- **Primary Blue (Dark)**: `#4A9EFF` - Lighter blue for dark backgrounds
- **Accent Purple (Dark)**: `#A855F7` - Lighter purple for dark backgrounds
- **Background (Dark)**: `#1E2329` - Main dark background
- **Card Background (Dark)**: `#252B32` - Card backgrounds in dark mode
- **Text Primary (Dark)**: `#EEEFF1` - High contrast text for dark backgrounds
- **Text Secondary (Dark)**: `#A8ACB0` - Medium contrast text for dark backgrounds

#### Using Design System Components
```tsx
import {
  DesignSystemWrapper,
  DesignSystemCard,
  DesignSystemButton,
  DesignSystemText
} from '@/components/ui/design-system-wrapper';

// Page wrapper with automatic theme support
<DesignSystemWrapper>
  <DesignSystemCard>
    <DesignSystemText variant="primary" size="lg" weight="semibold">
      Title Text
    </DesignSystemText>
    <DesignSystemText variant="secondary">
      Secondary text that adapts to theme
    </DesignSystemText>
    <DesignSystemButton variant="primary">
      Primary Action
    </DesignSystemButton>
  </DesignSystemCard>
</DesignSystemWrapper>
```

#### Theme Toggle Integration
```tsx
import { ThemeToggle } from '@/components/layout/theme-toggle';

// Add theme toggle to navigation
<ThemeToggle />
```

### Manual Dark Mode Styling
For components that need manual dark mode styling:

```tsx
// Using Tailwind classes
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
  Content that adapts to theme
</div>

// Using CSS variables (defined in design-system.css)
<div style={{
  backgroundColor: 'var(--color-background)',
  color: 'var(--color-text-primary)'
}}>
  Content using CSS variables
</div>
```

## Quick Reference

### Color Variables (CSS Custom Properties)
```css
:root {
  /* Light Mode */
  --primary-blue: #0055A4;
  --accent-purple: #663399;
  --background-light: #F5F5F5;
  --text-dark: #333333;
  --text-medium: #666666;
  --success: #22C55E;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
}

.dark {
  /* Dark Mode */
  --primary-blue: #4A9EFF;
  --accent-purple: #A855F7;
  --background-dark: #1E2329;
  --text-light: #EEEFF1;
  --text-medium-light: #A8ACB0;
  --success: #4ADE80;
  --warning: #FBBF24;
  --error: #F87171;
  --info: #60A5FA;
}
```

### Common Class Combinations
```tsx
// Primary button
"hover:bg-blue-700 transition-colors shadow-md"

// Outline button
"hover:bg-blue-50 transition-colors shadow-sm"

// Card container
"hover:shadow-md transition-shadow"

// Page layout
"min-h-screen container mx-auto p-6 space-y-6"
```
