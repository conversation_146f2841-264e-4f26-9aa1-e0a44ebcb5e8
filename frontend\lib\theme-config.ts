/**
 * Comprehensive Theme Configuration for Comply Checker
 * Implements Standard, Dark, and System themes with WCAG AA compliance
 */

export type ThemeName = 'standard' | 'dark' | 'system';

export interface ThemeColors {
  // Primary Colors
  primary: string;
  primaryLight: string;
  primaryDark: string;
  primaryForeground: string;
  
  // Accent Colors
  accent: string;
  accentLight: string;
  accentDark: string;
  accentForeground: string;
  
  // Background Colors
  background: string;
  backgroundSecondary: string;
  backgroundTertiary: string;
  
  // Text Colors
  textPrimary: string;
  textSecondary: string;
  textTertiary: string;
  
  // Border Colors
  border: string;
  borderLight: string;
  borderDark: string;
  
  // Status Colors
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  error: string;
  errorForeground: string;
  info: string;
  infoForeground: string;
  
  // Component Colors
  card: string;
  cardForeground: string;
  input: string;
  inputForeground: string;
  muted: string;
  mutedForeground: string;
  
  // Interactive States
  hover: string;
  focus: string;
  active: string;
}

/**
 * Standard Theme (Light Mode) - Default Design System Colors
 * WCAG AA Compliant with established color palette
 */
export const standardTheme: ThemeColors = {
  // Primary Colors - Design System Blue
  primary: '#0055A4',
  primaryLight: '#1E6BB8',
  primaryDark: '#003D7A',
  primaryForeground: '#FFFFFF',
  
  // Accent Colors - Design System Purple
  accent: '#663399',
  accentLight: '#7A4DB8',
  accentDark: '#4D2673',
  accentForeground: '#FFFFFF',
  
  // Background Colors
  background: '#F5F5F5',           // Light gray background
  backgroundSecondary: '#FFFFFF',  // White for cards
  backgroundTertiary: '#FAFAFA',   // Very light gray
  
  // Text Colors
  textPrimary: '#333333',          // Dark gray primary text
  textSecondary: '#666666',        // Medium gray secondary text
  textTertiary: '#999999',         // Light gray tertiary text
  
  // Border Colors
  border: '#E5E5E5',               // Light gray borders
  borderLight: '#F0F0F0',          // Very light borders
  borderDark: '#CCCCCC',           // Darker borders
  
  // Status Colors - Project Rules Compliant
  success: '#059669',              // Low Risk Green (Project Rules)
  successForeground: '#FFFFFF',
  warning: '#D97706',              // Medium Risk Yellow (Project Rules)
  warningForeground: '#FFFFFF',
  error: '#DC2626',                // Critical Risk Red (Project Rules)
  errorForeground: '#FFFFFF',
  info: '#3B82F6',                 // Info blue
  infoForeground: '#FFFFFF',
  
  // Component Colors
  card: '#FFFFFF',                 // White cards
  cardForeground: '#333333',       // Dark text on cards
  input: '#FFFFFF',                // White input backgrounds
  inputForeground: '#333333',      // Dark text in inputs
  muted: '#F5F5F5',                // Muted background
  mutedForeground: '#666666',      // Muted text
  
  // Interactive States
  hover: '#F0F8FF',                // Light blue hover
  focus: '#0055A4',                // Primary blue focus
  active: '#003D7A',               // Dark blue active
};

/**
 * Dark Theme - WCAG AA Compliant Dark Mode
 * Maintains design system identity with adjusted colors for dark backgrounds
 */
export const darkTheme: ThemeColors = {
  // Primary Colors - Lighter blue for dark backgrounds
  primary: '#4A9EFF',
  primaryLight: '#6BB3FF',
  primaryDark: '#2E8BFF',
  primaryForeground: '#1E2329',
  
  // Accent Colors - Lighter purple for dark backgrounds
  accent: '#A855F7',
  accentLight: '#C084FC',
  accentDark: '#9333EA',
  accentForeground: '#1E2329',
  
  // Background Colors
  background: '#1E2329',           // Dark blue-gray background
  backgroundSecondary: '#252B32',  // Slightly lighter for cards
  backgroundTertiary: '#2F363E',   // Even lighter for sections
  
  // Text Colors
  textPrimary: '#EEEFF1',          // Light text for high contrast
  textSecondary: '#A8ACB0',        // Medium light text
  textTertiary: '#6B7280',         // Lower contrast text
  
  // Border Colors
  border: '#424952',               // Dark border
  borderLight: '#353C45',          // Lighter dark border
  borderDark: '#2F363E',           // Darker border
  
  // Status Colors - Adjusted for dark backgrounds
  success: '#4ADE80',              // Light green
  successForeground: '#1E2329',
  warning: '#FBBF24',              // Light orange
  warningForeground: '#1E2329',
  error: '#F87171',                // Light red
  errorForeground: '#1E2329',
  info: '#60A5FA',                 // Light blue
  infoForeground: '#1E2329',
  
  // Component Colors
  card: '#252B32',                 // Dark card background
  cardForeground: '#EEEFF1',       // Light text on cards
  input: '#353C45',                // Dark input background
  inputForeground: '#EEEFF1',      // Light text in inputs
  muted: '#2F363E',                // Dark muted background
  mutedForeground: '#A8ACB0',      // Medium light muted text
  
  // Interactive States
  hover: '#353C45',                // Dark hover
  focus: '#4A9EFF',                // Light blue focus
  active: '#2E8BFF',               // Darker blue active
};

/**
 * Theme Configuration Object
 */
export const themeConfig = {
  standard: standardTheme,
  dark: darkTheme,
  system: standardTheme, // Will be dynamically determined
};

/**
 * Get theme colors by theme name
 * @param themeName - The theme name ('standard', 'dark', or 'system')
 * @returns ThemeColors object with all color definitions for the specified theme
 * @example
 * ```typescript
 * const colors = getThemeColors('standard');
 * console.log(colors.primary); // '#0055A4'
 * ```
 */
export function getThemeColors(themeName: ThemeName): ThemeColors {
  if (themeName === 'system') {
    // Detect system preference
    if (typeof window !== 'undefined') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      return prefersDark ? darkTheme : standardTheme;
    }
    return standardTheme; // Default to standard on server
  }
  
  return themeConfig[themeName] || standardTheme;
}

/**
 * Apply theme colors to CSS custom properties
 */
export function applyThemeColors(themeName: ThemeName): void {
  if (typeof window === 'undefined') return;
  
  const colors = getThemeColors(themeName);
  const root = document.documentElement;
  
  // Apply design system colors
  root.style.setProperty('--color-primary', colors.primary);
  root.style.setProperty('--color-primary-light', colors.primaryLight);
  root.style.setProperty('--color-primary-dark', colors.primaryDark);
  root.style.setProperty('--color-accent', colors.accent);
  root.style.setProperty('--color-accent-light', colors.accentLight);
  root.style.setProperty('--color-accent-dark', colors.accentDark);
  root.style.setProperty('--color-background', colors.background);
  root.style.setProperty('--color-background-secondary', colors.backgroundSecondary);
  root.style.setProperty('--color-text-primary', colors.textPrimary);
  root.style.setProperty('--color-text-secondary', colors.textSecondary);
  root.style.setProperty('--color-border', colors.border);
  root.style.setProperty('--color-success', colors.success);
  root.style.setProperty('--color-warning', colors.warning);
  root.style.setProperty('--color-error', colors.error);
  root.style.setProperty('--color-info', colors.info);
}

/**
 * Get readable theme display names
 */
export function getThemeDisplayName(themeName: ThemeName): string {
  const displayNames: Record<ThemeName, string> = {
    standard: 'Standard',
    dark: 'Dark',
    system: 'System',
  };
  
  return displayNames[themeName] || 'Standard';
}

/**
 * Get theme descriptions
 */
export function getThemeDescription(themeName: ThemeName): string {
  const descriptions: Record<ThemeName, string> = {
    standard: 'Light mode with design system colors',
    dark: 'WCAG AA compliant dark mode',
    system: 'Follows your system preference',
  };
  
  return descriptions[themeName] || '';
}

/**
 * Default theme configuration
 */
export const DEFAULT_THEME: ThemeName = 'standard';

/**
 * Available theme options for UI components
 */
export const THEME_OPTIONS: Array<{
  value: ThemeName;
  label: string;
  description: string;
}> = [
  {
    value: 'standard',
    label: getThemeDisplayName('standard'),
    description: getThemeDescription('standard'),
  },
  {
    value: 'dark',
    label: getThemeDisplayName('dark'),
    description: getThemeDescription('dark'),
  },
  {
    value: 'system',
    label: getThemeDisplayName('system'),
    description: getThemeDescription('system'),
  },
];
