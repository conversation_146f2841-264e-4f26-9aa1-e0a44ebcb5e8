'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Checkbox } from '@/components/ui/Checkbox';
import { Check } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Badge } from '@/components/ui/Badge';
import {
  Loader2,
  Shield,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Play,
  Settings,
  FileText,
  Users,
  Scale,
  ArrowLeft,
  Info
} from 'lucide-react';
import { useThemeColors, useButtonStyles, useCardStyles } from '@/hooks/useThemeColors';

interface PrivacyScanConfig {
  targetUrl: string;
  timeout: number;
  maxRedirects: number;
  userAgent: string;
  includeSubdomains: boolean;
  enableLevel1: boolean;
  enableLevel2: boolean;
  enableLevel3: boolean;
  cacheResults: boolean;
  generateReport: boolean;
}

export default function HipaaPrivacyScanPage() {
  const router = useRouter();

  // Theme hooks
  const colors = useThemeColors();
  const buttonStyles = useButtonStyles();
  const cardStyles = useCardStyles();

  const [scanConfig, setScanConfig] = useState<PrivacyScanConfig>({
    targetUrl: '',
    timeout: 300000, // 5 minutes
    maxRedirects: 5,
    userAgent: 'HIPAA-Privacy-Scanner/1.0',
    includeSubdomains: false,
    enableLevel1: true,
    enableLevel2: true,
    enableLevel3: true, // Always enable all levels
    cacheResults: true,
    generateReport: true,
  });

  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scanProgress, setScanProgress] = useState(0);

  const handleBackToDashboard = () => {
    router.push('/dashboard/hipaa');
  };

  const handleStartScan = async () => {
    if (!scanConfig.targetUrl.trim()) {
      setError('Please enter a valid URL to scan');
      return;
    }

    setIsScanning(true);
    setError(null);
    setScanProgress(0);

    try {
      // Simulate scan progress
      const progressInterval = setInterval(() => {
        setScanProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 1000);

      const apiUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3001/api/v1';
      console.log('🚀 Starting privacy scan with URL:', `${apiUrl}/hipaa-privacy/scan`);
      console.log('📋 Scan config:', scanConfig);

      const response = await fetch(`${apiUrl}/hipaa-privacy/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scanConfig),
      });

      clearInterval(progressInterval);
      setScanProgress(100);

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Response error:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('📊 Response data:', result);

      if (result.success) {
        console.log('✅ Scan completed successfully');
        // Store the scan result in localStorage as backup (results are now also saved to database)
        if (result.data.result) {
          localStorage.setItem(`privacy-scan-${result.data.scanId}`, JSON.stringify(result.data.result));
          console.log('💾 Scan result stored in localStorage with ID:', result.data.scanId);
        }
        // Redirect to the dedicated scan results page
        router.push(`/dashboard/hipaa/privacy/${result.data.scanId}`);
      } else {
        console.error('❌ Scan failed:', result.error);
        throw new Error(result.error || 'Scan failed');
      }
    } catch (err) {
      console.error('❌ Privacy scan error:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsScanning(false);
      setScanProgress(0);
    }
  };

  const updateConfig = (key: keyof PrivacyScanConfig, value: any) => {
    setScanConfig(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background, #F5F5F5)', color: 'var(--color-text-primary, #333333)' }}>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToDashboard}
              style={buttonStyles.secondary.style}
              className={buttonStyles.secondary.className}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to HIPAA Dashboard
            </Button>
            <div className="flex items-center gap-3">
              <FileText className="h-8 w-8" style={{ color: colors.accent }} />
              <div>
                <h1 className="text-3xl font-bold" style={{ color: colors.textPrimary }}>
                  HIPAA Privacy Policy Scan
                </h1>
                <p style={{ color: colors.textSecondary }}>
                  3-Level HIPAA privacy policy compliance analysis
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert
            style={{
              backgroundColor: colors.isDark ? '#2D1B1B' : '#FEF2F2',
              borderColor: colors.error,
            }}
          >
            <AlertCircle className="h-4 w-4" style={{ color: colors.error }} />
            <AlertDescription style={{ color: colors.error }}>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Scan Configuration */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Configuration */}
          <div className="lg:col-span-2 space-y-6">
            <Card style={cardStyles.default.style}>
              <CardHeader>
                <CardTitle style={{ color: colors.textPrimary }}>Scan Configuration</CardTitle>
                <CardDescription style={{ color: colors.textSecondary }}>
                  Configure your HIPAA privacy policy compliance scan
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="targetUrl">Target URL *</Label>
                  <Input
                    id="targetUrl"
                    type="url"
                    placeholder="https://example.com"
                    value={scanConfig.targetUrl}
                    onChange={(e) => updateConfig('targetUrl', e.target.value)}
                    disabled={isScanning}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Timeout (seconds)</Label>
                    <Input
                      id="timeout"
                      type="number"
                      value={scanConfig.timeout / 1000}
                      onChange={(e) => updateConfig('timeout', parseInt(e.target.value) * 1000)}
                      disabled={isScanning}
                      min="30"
                      max="600"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxRedirects">Max Redirects</Label>
                    <Input
                      id="maxRedirects"
                      type="number"
                      value={scanConfig.maxRedirects}
                      onChange={(e) => updateConfig('maxRedirects', parseInt(e.target.value))}
                      disabled={isScanning}
                      min="0"
                      max="10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="userAgent">User Agent</Label>
                  <Input
                    id="userAgent"
                    value={scanConfig.userAgent}
                    onChange={(e) => updateConfig('userAgent', e.target.value)}
                    disabled={isScanning}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Analysis Levels */}
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardHeader>
                <CardTitle style={{ color: '#333333' }}>Analysis Levels</CardTitle>
                <CardDescription style={{ color: '#666666' }}>
                  All analysis levels are automatically enabled for comprehensive scanning
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className="flex items-center justify-center w-5 h-5 rounded-full" style={{ backgroundColor: '#22C55E' }}>
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Badge variant="outline" style={{ borderColor: '#22C55E', color: '#22C55E' }}>
                        Level 1
                      </Badge>
                      Pattern Matching Analysis
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Basic pattern matching for HIPAA compliance keywords
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div className="flex items-center justify-center w-5 h-5 rounded-full" style={{ backgroundColor: '#22C55E' }}>
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Badge variant="outline" style={{ borderColor: '#F59E0B', color: '#F59E0B' }}>
                        Level 2
                      </Badge>
                      NLP Analysis
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Natural Language Processing for context understanding
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <div className="flex items-center justify-center w-5 h-5 rounded-full" style={{ backgroundColor: '#22C55E' }}>
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <div className="grid gap-1.5 leading-none">
                    <Label className="flex items-center gap-2">
                      <Badge variant="outline" style={{ borderColor: '#EF4444', color: '#EF4444' }}>
                        Level 3
                      </Badge>
                      AI-Powered Analysis
                    </Label>
                    <p className="text-xs" style={{ color: '#666666' }}>
                      Advanced AI analysis for comprehensive compliance assessment
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Scan Status */}
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardHeader>
                <CardTitle style={{ color: '#333333' }}>Scan Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>Timeout:</span>
                  <Badge variant="outline" style={{ borderColor: '#0055A4', color: '#0055A4' }}>
                    {scanConfig.timeout / 1000}s
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>Max Redirects:</span>
                  <Badge variant="outline" style={{ borderColor: '#0055A4', color: '#0055A4' }}>
                    {scanConfig.maxRedirects}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium" style={{ color: '#333333' }}>Analysis Levels:</span>
                  <Badge variant="outline" style={{ borderColor: '#22C55E', color: '#22C55E' }}>
                    All Enabled
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Start Scan */}
            <Card style={{ backgroundColor: 'white', color: '#333333' }}>
              <CardContent className="pt-6">
                <Button
                  onClick={handleStartScan}
                  disabled={isScanning || !scanConfig.targetUrl.trim()}
                  className="w-full hover:bg-blue-700 transition-colors"
                  style={{ 
                    backgroundColor: '#0055A4', 
                    color: 'white',
                    border: 'none'
                  }}
                >
                  {isScanning ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Scanning... {scanProgress}%
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Start Privacy Scan
                    </>
                  )}
                </Button>

                {isScanning && (
                  <div className="mt-4">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="h-2 rounded-full transition-all duration-300"
                        style={{ 
                          backgroundColor: '#0055A4',
                          width: `${scanProgress}%`
                        }}
                      ></div>
                    </div>
                    <p className="text-sm mt-2 text-center" style={{ color: '#666666' }}>
                      Analyzing privacy policy compliance...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Info Card */}
            <Card style={{ backgroundColor: '#F0F9FF', color: '#333333' }}>
              <CardContent className="pt-6">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 mt-0.5" style={{ color: '#0055A4' }} />
                  <div>
                    <h4 className="font-semibold mb-2" style={{ color: '#333333' }}>
                      About HIPAA Privacy Scans
                    </h4>
                    <p className="text-sm" style={{ color: '#666666' }}>
                      Our 3-level analysis system provides comprehensive HIPAA privacy policy compliance assessment using pattern matching, NLP, and AI-powered analysis.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
