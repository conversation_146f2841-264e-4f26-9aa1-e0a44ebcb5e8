/**
 * Comprehensive type definitions for HIPAA analysis system
 * Replaces all 'any' types with proper TypeScript interfaces
 */

// HIPAA Check Category Types
export type HipaaCheckCategory =
  | 'privacy_policy'
  | 'patient_rights'
  | 'contact_requirements'
  | 'uses_disclosures'
  | 'security_safeguards'
  | 'administrative_safeguards'
  | 'physical_safeguards'
  | 'technical_safeguards'
  | 'breach_notification'
  | 'business_associates'
  | 'general';

// Analysis Level Types
export interface AnalysisLevel {
  level: number;
  method: string;
  score: string | number;
  confidence: string | number;
  processingTime: number;
  findings?: Finding[];
  foundPatterns?: number;
  totalPatterns?: number;
  entities?: EntityData;
  privacyStatements?: PrivacyStatement[];
  rightsStatements?: RightsStatement[];
  identifiedGaps?: ComplianceGap[];
  riskFactors?: RiskFactor[];
  recommendations?: Recommendation[];
  positiveFindings?: PositiveFinding[];
  [key: string]: unknown;
}

// Entity Data Types
export interface EntityData {
  people?: Person[] | string[];
  organizations?: Organization[] | string[];
  phoneNumbers?: PhoneNumber[] | string[];
  emails?: Email[] | string[];
}

export interface Person {
  name: string;
  role?: string;
  [key: string]: unknown;
}

export interface Organization {
  name: string;
  type?: string;
  [key: string]: unknown;
}

export interface PhoneNumber {
  number: string;
  type?: string;
  [key: string]: unknown;
}

export interface Email {
  address: string;
  type?: string;
  [key: string]: unknown;
}

// Statement Types
export interface PrivacyStatement {
  content?: string;
  text?: string;
  type?: string;
  confidence?: number;
  location?: number;
  [key: string]: unknown;
}

export interface RightsStatement {
  content?: string;
  text?: string;
  type?: string;
  rightType?: string;
  confidence?: number;
  location?: number;
  [key: string]: unknown;
}

// Finding Types
export interface Finding {
  id?: string;
  content?: string;
  requirement?: string;
  text?: string;
  message?: string;
  type?: string;
  severity?: string;
  confidence?: number;
  [key: string]: unknown;
}

// Compliance Types
export interface ComplianceGap {
  id: string;
  description: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  category?: string;
  [key: string]: unknown;
}

export interface RiskFactor {
  id?: string;
  type?: string;
  description: string;
  severity?: string;
  level?: 'HIGH' | 'MEDIUM' | 'LOW';
  likelihood?: 'low' | 'medium' | 'high';
  impact?: 'low' | 'medium' | 'high';
  mitigation?: string;
  recommendation?: string;
  category?: string;
  [key: string]: unknown;
}

export interface Recommendation {
  id?: string;
  title: string;
  description: string;
  priority: number | string;
  category?: string;
  effort?: string;
  timeline?: string;
  implementation: string;
  impact: string;
  [key: string]: unknown;
}

// Positive Finding Types
export interface PositiveFinding {
  id: string;
  checkId: string;
  category: string;
  title: string;
  description: string;
  evidenceText: string;
  confidenceScore: number;
  complianceContribution: number;
  reinforcementMessage: string;
  hipaaRequirement: string;
  bestPracticeLevel: 'basic' | 'good' | 'excellent';
}

// Check Types
export interface HipaaCheck {
  checkId: string;
  name: string;
  passed: boolean;
  score: string | null;
  confidence: string;
  analysisLevels: AnalysisLevel[];
  details?: {
    findings?: Finding[];
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

// Enhanced Results Types
export interface EnhancedHipaaResults {
  targetUrl: string;
  overallScore: string;
  overallPassed: boolean;
  summary: {
    totalChecks: number;
    passedChecks: number;
    failedChecks: number;
    complianceLevel: string;
  };
  checksBreakdown: HipaaCheck[];
  checks?: HipaaCheck[]; // Alternative property name for backwards compatibility
  recommendations?: Recommendation[];
  recommendationsCount: number;
  metadata: {
    processingTime: number;
    version: string;
    analysisLevelsUsed: number[];
  };
  timestamp?: string;
  [key: string]: unknown;
}

// Scan Data Types
export interface ScanData {
  id: string;
  url: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  enhancedHipaaResults?: EnhancedHipaaResults;
  findings?: Finding[];
  error?: string;
  completed_at?: string;
  [key: string]: unknown;
}

// Transform Result Types
export interface TransformedAnalysisResult {
  overallScore: number;
  privacyPolicyPresent: boolean;
  privacyPolicyUrl?: string;
  contentAnalysisScore: number;
  contactInformationScore: number;
  analysisLevels: {
    level1: {
      score: number;
      method: 'pattern_matching';
      findings: string[];
      processingTime: number;
    };
    level2: {
      score: number;
      method: 'nlp_analysis';
      entities: {
        people: Person[];
        organizations: Organization[];
        phoneNumbers: PhoneNumber[];
        emails: Email[];
      };
      statements: {
        privacy: number;
        rights: number;
      };
      processingTime: number;
    };
    level3: {
      score: number;
      method: 'ai_analysis';
      complianceGaps: ComplianceGap[];
      recommendations: Recommendation[];
      processingTime: number;
    };
  };
  findings: Finding[];
  recommendations: Recommendation[];
  totalProcessingTime: number;
  analysisTimestamp: string;
}

// Component Props Types
export interface SimpleHipaaResultsProps {
  scan: ScanData;
}

// Level Results Types (for backwards compatibility)
export interface LevelResults {
  level1?: AnalysisLevel;
  level2?: AnalysisLevel;
  level3?: AnalysisLevel;
  [key: string]: unknown;
}

// Type adapters for backend response conversion
export interface BackendEntityData {
  people?: string[];
  organizations?: string[];
  phoneNumbers?: string[];
  emails?: string[];
}

export interface BackendRecommendation {
  title: string;
  description: string;
  priority: string;
  [key: string]: unknown;
}

export interface BackendRiskFactor {
  type: string;
  severity: string;
  description: string;
  recommendation: string;
  [key: string]: unknown;
}

// Type conversion utilities
export const convertEntityData = (backendData?: BackendEntityData): EntityData | undefined => {
  if (!backendData) return undefined;

  return {
    people: backendData.people?.map(name => ({ name })),
    organizations: backendData.organizations?.map(name => ({ name })),
    phoneNumbers: backendData.phoneNumbers?.map(number => ({ number })),
    emails: backendData.emails?.map(address => ({ address })),
  };
};

export const convertRecommendation = (backendRec: BackendRecommendation): Recommendation => {
  return {
    id: `rec-${Date.now()}`,
    implementation: 'Implementation details to be provided',
    impact: 'medium',
    effort: 'moderate',
    ...backendRec,
    title: backendRec.title,
    description: backendRec.description,
    priority: backendRec.priority,
  };
};

export const convertRiskFactor = (backendRisk: BackendRiskFactor): RiskFactor => {
  return {
    id: `risk-${Date.now()}`,
    likelihood: 'medium',
    impact: 'medium',
    mitigation: backendRisk.recommendation,
    ...backendRisk,
    type: backendRisk.type,
    description: backendRisk.description,
    severity: backendRisk.severity,
  };
};

// Backend AI Recommendation type (from Level 3 analysis)
export interface AIRecommendation {
  priority: number;
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant' | 'extensive';
  impact: 'low' | 'medium' | 'high';
}

// Backend HIPAA Recommendation type (from scan results) - flexible to handle actual backend response
export interface HipaaRecommendation {
  id?: string;
  priority?: number;
  title: string;
  description?: string;
  category?: string | object;
  effort?: string;
  impact?: string;
  timeline?: string;
  resources?: unknown[];
  relatedChecks?: string[];
  [key: string]: unknown;
}
