'use client';

import React, { useEffect } from 'react';
import { useTheme } from 'next-themes';
import { applyThemeColors, type ThemeName } from '@/lib/theme-config';

/**
 * ThemeEnforcer component that ensures the Standard theme is properly applied
 * and maintains consistency across the entire application.
 */
interface ThemeEnforcerProps {
  children: React.ReactNode;
}

export function ThemeEnforcer({ children }: ThemeEnforcerProps): JSX.Element {
  const { theme, resolvedTheme, setTheme } = useTheme();

  useEffect(() => {
    // Ensure Standard theme is applied by default if no theme is set
    if (!theme && !resolvedTheme) {
      setTheme('standard');
    }

    // Apply theme colors to CSS custom properties
    const currentTheme = (resolvedTheme || theme || 'standard') as ThemeName;
    applyThemeColors(currentTheme);

    // Add theme class to body for global styling
    const body = document.body;
    const html = document.documentElement;
    
    // Remove any existing theme classes
    body.classList.remove('theme-standard', 'theme-dark', 'theme-system');
    html.classList.remove('theme-standard', 'theme-dark', 'theme-system');
    
    // Add current theme class
    const themeClass = `theme-${currentTheme}`;
    body.classList.add(themeClass);
    html.classList.add(themeClass);

    // Apply global styles based on theme
    if (currentTheme === 'standard') {
      body.style.backgroundColor = 'var(--color-background, #F5F5F5)';
      body.style.color = 'var(--color-text-primary, #333333)';
    } else if (currentTheme === 'dark') {
      body.style.backgroundColor = 'var(--color-background, #1E2329)';
      body.style.color = 'var(--color-text-primary, #EEEFF1)';
    }

    // Ensure smooth transitions
    body.style.transition = 'background-color 0.2s ease, color 0.2s ease';

  }, [theme, resolvedTheme, setTheme]);

  // Monitor for theme changes and reapply colors
  useEffect(() => {
    const handleThemeChange = () => {
      const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'standard';
      applyThemeColors(currentTheme as ThemeName);
    };

    // Listen for class changes on the html element (next-themes behavior)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          handleThemeChange();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => observer.disconnect();
  }, []);

  return (
    <div className="theme-enforcer-wrapper">
      {children}
    </div>
  );
}

/**
 * PageWrapper component that applies consistent theme styling to pages
 */
interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export function PageWrapper({ children, className = '', style = {} }: PageWrapperProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const pageStyle: React.CSSProperties = {
    backgroundColor: isDark ? 'var(--color-background, #1E2329)' : 'var(--color-background, #F5F5F5)',
    color: isDark ? 'var(--color-text-primary, #EEEFF1)' : 'var(--color-text-primary, #333333)',
    minHeight: '100vh',
    transition: 'background-color 0.2s ease, color 0.2s ease',
    ...style,
  };

  return (
    <div 
      className={`page-wrapper ${className}`}
      style={pageStyle}
    >
      {children}
    </div>
  );
}

/**
 * CardWrapper component that applies consistent theme styling to cards
 */
interface CardWrapperProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  variant?: 'default' | 'elevated' | 'muted';
}

export function CardWrapper({ 
  children, 
  className = '', 
  style = {},
  variant = 'default'
}: CardWrapperProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const getCardStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      borderRadius: '0.5rem',
      transition: 'all 0.2s ease',
      ...style,
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          backgroundColor: isDark ? 'var(--color-card, #252B32)' : 'var(--color-card, #FFFFFF)',
          color: isDark ? 'var(--color-card-foreground, #EEEFF1)' : 'var(--color-card-foreground, #333333)',
          border: `1px solid ${isDark ? 'var(--color-border, #424952)' : 'var(--color-border, #E5E5E5)'}`,
          boxShadow: isDark ? '0 4px 6px -1px rgba(0, 0, 0, 0.4)' : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        };
      case 'muted':
        return {
          ...baseStyle,
          backgroundColor: isDark ? 'var(--color-muted, #2F363E)' : 'var(--color-muted, #F5F5F5)',
          color: isDark ? 'var(--color-muted-foreground, #A8ACB0)' : 'var(--color-muted-foreground, #666666)',
          border: `1px solid ${isDark ? 'var(--color-border-light, #353C45)' : 'var(--color-border-light, #F0F0F0)'}`,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: isDark ? 'var(--color-card, #252B32)' : 'var(--color-card, #FFFFFF)',
          color: isDark ? 'var(--color-card-foreground, #EEEFF1)' : 'var(--color-card-foreground, #333333)',
          border: `1px solid ${isDark ? 'var(--color-border, #424952)' : 'var(--color-border, #E5E5E5)'}`,
          boxShadow: isDark ? '0 1px 2px 0 rgba(0, 0, 0, 0.3)' : '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        };
    }
  };

  return (
    <div 
      className={`card-wrapper ${className}`}
      style={getCardStyle()}
    >
      {children}
    </div>
  );
}

/**
 * ButtonWrapper component that applies consistent theme styling to buttons
 */
interface ButtonWrapperProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost';
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export function ButtonWrapper({ 
  children, 
  variant = 'primary',
  className = '', 
  style = {},
  onClick,
  disabled = false,
  type = 'button'
}: ButtonWrapperProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const getButtonStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      border: 'none',
      borderRadius: '0.375rem',
      fontWeight: '500',
      cursor: disabled ? 'not-allowed' : 'pointer',
      transition: 'all 0.2s ease',
      opacity: disabled ? 0.6 : 1,
      padding: '0.75rem 1.5rem',
      ...style,
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: isDark ? 'var(--color-primary, #4A9EFF)' : 'var(--color-primary, #0055A4)',
          color: isDark ? 'var(--color-primary-foreground, #1E2329)' : 'var(--color-primary-foreground, #FFFFFF)',
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          color: isDark ? 'var(--color-primary, #4A9EFF)' : 'var(--color-primary, #0055A4)',
          border: `1px solid ${isDark ? 'var(--color-primary, #4A9EFF)' : 'var(--color-primary, #0055A4)'}`,
        };
      case 'accent':
        return {
          ...baseStyle,
          backgroundColor: isDark ? 'var(--color-accent, #A855F7)' : 'var(--color-accent, #663399)',
          color: isDark ? 'var(--color-accent-foreground, #1E2329)' : 'var(--color-accent-foreground, #FFFFFF)',
        };
      case 'ghost':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          color: isDark ? 'var(--color-text-primary, #EEEFF1)' : 'var(--color-text-primary, #333333)',
        };
      default:
        return baseStyle;
    }
  };

  return (
    <button 
      type={type}
      className={`button-wrapper ${className} hover:opacity-90`}
      style={getButtonStyle()}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
