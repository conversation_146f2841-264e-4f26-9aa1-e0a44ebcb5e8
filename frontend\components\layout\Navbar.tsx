'use client';

import React from 'react';
import { useAuth } from '../../context/AuthContext';
import Link from 'next/link';
import { ThemeToggle } from './theme-toggle';
import { useThemeColors, useButtonStyles } from '@/hooks/useThemeColors';

const Navbar: React.FC = () => {
  const { authenticated, login, logout, profile } = useAuth();
  const colors = useThemeColors();
  const buttonStyles = useButtonStyles();

  // Navigation bar styling using theme colors
  const navStyle = {
    backgroundColor: colors.isDark ? '#1F2937' : '#374151', // Dark gray for navbar
    color: '#FFFFFF',
    borderBottom: `1px solid ${colors.border}`,
  };

  return (
    <nav
      className="p-4 shadow-md w-full transition-colors"
      style={navStyle}
    >
      <div className="container mx-auto flex justify-between items-center">
        <Link
          href="/"
          className="text-xl font-bold hover:opacity-80 transition-opacity"
          style={{ color: '#FFFFFF' }}
        >
          Comply Checker
        </Link>
        <div className="flex items-center gap-4">
          {/* Theme Toggle - Always visible */}
          <ThemeToggle />

          {!authenticated ? (
            <button
              onClick={() => login()}
              style={buttonStyles.primary.style}
              className={`${buttonStyles.primary.className} font-bold py-2 px-4 rounded`}
            >
              Login
            </button>
          ) : (
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard/scan/new"
                className="text-sm px-3 py-2 rounded-md transition-colors"
                style={{
                  backgroundColor: colors.success,
                  color: colors.successForeground,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '0.9';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '1';
                }}
              >
                New Scan
              </Link>
              <Link
                href="/dashboard/scans"
                className="text-sm px-3 py-2 rounded-md transition-colors"
                style={{
                  backgroundColor: colors.primary,
                  color: colors.primaryForeground,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.primaryLight;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = colors.primary;
                }}
              >
                My Scans
              </Link>

              {/* HIPAA Compliance Dropdown */}
              <div className="relative group">
                <button
                  className="text-sm px-3 py-2 rounded-md flex items-center transition-colors"
                  style={{
                    backgroundColor: colors.primary,
                    color: colors.primaryForeground,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.primaryLight;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = colors.primary;
                  }}
                >
                  HIPAA
                  <svg
                    className="w-4 h-4 ml-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <div
                  className="absolute right-0 mt-2 w-56 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50"
                  style={{
                    backgroundColor: colors.backgroundSecondary,
                    border: `1px solid ${colors.border}`,
                  }}
                >
                  <div className="py-1">
                    <Link
                      href="/dashboard/hipaa"
                      className="block px-4 py-2 text-sm font-semibold border-b transition-colors"
                      style={{
                        color: colors.textPrimary,
                        borderColor: colors.border,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = colors.hover;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      📊 HIPAA Dashboard
                    </Link>
                    <div
                      className="px-4 py-1 text-xs font-medium uppercase tracking-wide"
                      style={{ color: colors.textSecondary }}
                    >
                      Modules
                    </div>
                    <Link
                      href="/dashboard/hipaa/privacy"
                      className="block px-4 py-2 text-sm transition-colors"
                      style={{ color: colors.textPrimary }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = colors.hover;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      📄 Privacy Policy Compliance
                    </Link>
                    <Link
                      href="/dashboard/hipaa/security"
                      className="block px-4 py-2 text-sm transition-colors"
                      style={{ color: colors.textPrimary }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = colors.hover;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      🔒 Security Compliance
                    </Link>
                  </div>
                </div>
              </div>

              <Link
                href="/guidance"
                className="text-sm px-3 py-2 rounded-md transition-colors"
                style={{
                  backgroundColor: colors.accent,
                  color: colors.accentForeground,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.accentLight;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = colors.accent;
                }}
              >
                Guidance
              </Link>
              <span className="text-sm" style={{ color: '#FFFFFF' }}>
                Welcome, {profile?.firstName || profile?.username || profile?.email || 'User'}
              </span>
              <button
                onClick={() => logout()}
                className="font-bold py-2 px-4 rounded transition-colors"
                style={{
                  backgroundColor: colors.error,
                  color: colors.errorForeground,
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '0.9';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '1';
                }}
              >
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
