import { HipaaPrivacyScanResult } from '@/types/hipaa-privacy';
import { HipaaSecurityScanResult } from '@/types/hipaa-security';

// Dashboard-specific types
export interface HipaaDashboardData {
  overview: {
    overallScore: number;
    riskLevel: 'critical' | 'high' | 'medium' | 'low';
    complianceStatus: 'compliant' | 'partially_compliant' | 'non_compliant';
    lastScanDate: string;
    totalScans: number;
  };
  privacyModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
    recentScans: HipaaPrivacyScanResult[];
  };
  securityModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
    recentScans: HipaaSecurityScanResult[];
  };
  recentActivity: ScanActivity[];
}

export interface ScanActivity {
  id: string;
  type: 'privacy' | 'security';
  url: string;
  timestamp: string;
  score: number;
  status: 'completed' | 'failed' | 'running';
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
}

export interface DashboardMetrics {
  totalScans: number;
  averageScore: number;
  riskDistribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  complianceRate: number;
  trendsData: {
    period: string;
    score: number;
    scans: number;
  }[];
}

/**
 * HIPAA Dashboard API Service
 * Aggregates data from privacy and security modules
 */
export class HipaaDashboardService {
  private readonly baseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3001/api/v1';

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(): Promise<HipaaDashboardData> {
    try {
      // Try to fetch from the backend dashboard endpoint first
      const response = await fetch(`${this.baseUrl}/compliance/hipaa/dashboard`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          return data.data;
        }
      }

      // Fallback to individual module calls if dashboard endpoint fails
      const [privacyScans, securityScans] = await Promise.allSettled([
        this.getPrivacyScans(10), // Get last 10 scans
        this.getSecurityScans(10),
      ]);

      // Extract successful results or use empty arrays
      const privacyResults = privacyScans.status === 'fulfilled' ? privacyScans.value : [];
      const securityResults = securityScans.status === 'fulfilled' ? securityScans.value : [];

      // Calculate aggregated metrics
      const overview = this.calculateOverview(privacyResults, securityResults);
      const privacyModule = this.calculateModuleData(privacyResults);
      const securityModule = this.calculateModuleData(securityResults);
      const recentActivity = this.generateRecentActivity(privacyResults, securityResults);

      return {
        overview,
        privacyModule: {
          ...privacyModule,
          recentScans: privacyResults,
        },
        securityModule: {
          ...securityModule,
          recentScans: securityResults,
        },
        recentActivity,
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);

      // Final fallback: return default empty dashboard data
      return this.getDefaultDashboardData();
    }
  }

  /**
   * Get privacy scans from API
   */
  async getPrivacyScans(limit: number = 50): Promise<HipaaPrivacyScanResult[]> {
    try {
      // Use the actual backend endpoint
      const response = await fetch(`${this.baseUrl}/compliance/hipaa/privacy/scans?limit=${limit}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          return data.data;
        }
      }

      // Temporary fallback with mock data to test frontend rendering
      console.warn('No privacy scans found in backend, using mock data');
      return [
        {
          targetUrl: 'https://example-healthcare.com',
          timestamp: '2025-06-24T10:30:00Z',
          overallScore: 82,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 20,
            failedChecks: 4,
            criticalIssues: 1,
            highIssues: 2,
            mediumIssues: 1,
            lowIssues: 0,
            overallScore: 82,
            complianceLevel: 'mostly_compliant',
            riskLevel: 'medium',
            analysisLevelsUsed: [1, 2, 3],
          },
          checks: [],
          recommendations: [],
          metadata: {
            version: '1.0.0',
            processingTime: 45000,
            checksPerformed: 15,
            analysisLevelsUsed: [1, 2, 3],
            cacheHits: 3,
            errors: [],
            warnings: [],
            userAgent: 'HIPAA-Compliance-Scanner/1.0',
            scanOptions: {
              timeout: 30000,
              maxRedirects: 5,
              userAgent: 'HIPAA-Compliance-Scanner/1.0',
            },
          },
        },
        {
          targetUrl: 'https://medical-clinic.org',
          timestamp: '2025-06-23T14:15:00Z',
          overallScore: 91,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 22,
            failedChecks: 2,
            criticalIssues: 0,
            highIssues: 1,
            mediumIssues: 1,
            lowIssues: 0,
            overallScore: 91,
            complianceLevel: 'compliant',
            riskLevel: 'low',
            analysisLevelsUsed: [1, 2, 3],
          },
          checks: [],
          recommendations: [],
          metadata: {
            version: '1.0.0',
            processingTime: 38000,
            checksPerformed: 15,
            analysisLevelsUsed: [1, 2, 3],
            cacheHits: 2,
            errors: [],
            warnings: [],
            userAgent: 'HIPAA-Compliance-Scanner/1.0',
            scanOptions: {
              timeout: 30000,
              maxRedirects: 5,
              userAgent: 'HIPAA-Compliance-Scanner/1.0',
            },
          },
        },
      ];
    } catch (error) {
      console.error('Error fetching privacy scans:', error);
      return []; // Return empty array instead of throwing
    }
  }

  /**
   * Get security scans from API
   */
  async getSecurityScans(limit: number = 50): Promise<HipaaSecurityScanResult[]> {
    try {
      // Use the actual backend endpoint
      const response = await fetch(`${this.baseUrl}/compliance/hipaa/security/scans?limit=${limit}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          return data.data;
        }
      }

      // If no data available, return empty array instead of mock data
      console.warn('No security scans found in backend');
      return [];
    } catch (error) {
      console.error('Error fetching security scans:', error);
      return []; // Return empty array instead of throwing
    }
  }

  /**
   * Calculate overall HIPAA compliance score
   */
  calculateOverallScore(
    privacyScans: HipaaPrivacyScanResult[],
    securityScans: HipaaSecurityScanResult[],
  ): number {
    const privacyWeight = 0.4; // 40% weight for privacy
    const securityWeight = 0.6; // 60% weight for security

    const latestPrivacy = privacyScans[0];
    const latestSecurity = securityScans[0];

    const privacyScore = latestPrivacy?.summary?.overallScore || 0;
    const securityScore = latestSecurity?.overallScore || 0; // Security uses overallScore directly

    return Math.round(privacyScore * privacyWeight + securityScore * securityWeight);
  }

  /**
   * Determine risk level from score
   */
  getRiskLevelFromScore(score: number): 'critical' | 'high' | 'medium' | 'low' {
    if (score >= 90) return 'low';
    if (score >= 75) return 'medium';
    if (score >= 60) return 'high';
    return 'critical';
  }

  /**
   * Determine compliance status
   */
  getComplianceStatus(score: number): 'compliant' | 'partially_compliant' | 'non_compliant' {
    if (score >= 85) return 'compliant';
    if (score >= 60) return 'partially_compliant';
    return 'non_compliant';
  }

  /**
   * Helper to get timestamp from either scan type
   */
  private getTimestamp(scan: HipaaPrivacyScanResult | HipaaSecurityScanResult): string {
    if ('timestamp' in scan) {
      return scan.timestamp; // Privacy scan
    } else {
      return scan.scanTimestamp.toISOString(); // Security scan
    }
  }

  /**
   * Helper to get score from either scan type
   */
  private getScore(scan: HipaaPrivacyScanResult | HipaaSecurityScanResult): number {
    if ('summary' in scan) {
      return scan.summary?.overallScore || 0; // Privacy scan
    } else {
      return scan.overallScore || 0; // Security scan
    }
  }

  /**
   * Calculate overview metrics
   */
  private calculateOverview(
    privacyScans: HipaaPrivacyScanResult[],
    securityScans: HipaaSecurityScanResult[],
  ) {
    const overallScore = this.calculateOverallScore(privacyScans, securityScans);
    const riskLevel = this.getRiskLevelFromScore(overallScore);
    const complianceStatus = this.getComplianceStatus(overallScore);

    const allScans = [...privacyScans, ...securityScans];
    const lastScanDate =
      allScans.length > 0
        ? allScans.sort(
            (a, b) =>
              new Date(this.getTimestamp(b)).getTime() - new Date(this.getTimestamp(a)).getTime(),
          )[0]
        : null;

    return {
      overallScore,
      riskLevel,
      complianceStatus,
      lastScanDate: lastScanDate ? this.getTimestamp(lastScanDate) : new Date().toISOString(),
      totalScans: allScans.length,
    };
  }

  /**
   * Calculate module-specific data
   */
  private calculateModuleData(scans: (HipaaPrivacyScanResult | HipaaSecurityScanResult)[]) {
    if (scans.length === 0) {
      return {
        scanCount: 0,
        status: 'not_scanned' as const,
      };
    }

    const latestScan = scans[0];
    const latestScore = this.getScore(latestScan);
    const lastScanDate = this.getTimestamp(latestScan);

    // Determine status based on latest scan
    let status: 'active' | 'needs_attention' | 'not_scanned' = 'active';
    if (latestScore && latestScore < 70) {
      status = 'needs_attention';
    }

    return {
      latestScore,
      scanCount: scans.length,
      lastScanDate,
      status,
    };
  }

  /**
   * Generate recent activity timeline
   */
  private generateRecentActivity(
    privacyScans: HipaaPrivacyScanResult[],
    securityScans: HipaaSecurityScanResult[],
  ): ScanActivity[] {
    const activities: ScanActivity[] = [];

    // Convert privacy scans to activities
    privacyScans.slice(0, 5).forEach((scan, index) => {
      const timestamp = this.getTimestamp(scan);
      const score = this.getScore(scan);
      activities.push({
        id: `privacy-${Date.parse(timestamp)}-${index}`, // Generate ID from timestamp
        type: 'privacy',
        url: scan.targetUrl,
        timestamp,
        score,
        status: 'completed', // Assuming completed for now
        riskLevel: this.getRiskLevelFromScore(score),
      });
    });

    // Convert security scans to activities
    securityScans.slice(0, 5).forEach((scan) => {
      const timestamp = this.getTimestamp(scan);
      const score = this.getScore(scan);
      activities.push({
        id: scan.scanId,
        type: 'security',
        url: scan.targetUrl,
        timestamp,
        score,
        status: 'completed', // Assuming completed for now
        riskLevel: this.getRiskLevelFromScore(score),
      });
    });

    // Sort by timestamp (most recent first) and limit to 10
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);
  }

  /**
   * Start a new HIPAA privacy scan
   */
  async startPrivacyScan(
    targetUrl: string,
    options?: {
      enableLevel1?: boolean;
      enableLevel2?: boolean;
      enableLevel3?: boolean;
      timeout?: number;
    },
  ): Promise<HipaaPrivacyScanResult> {
    try {
      const response = await fetch(`${this.baseUrl}/compliance/hipaa/privacy/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUrl,
          enableLevel1: options?.enableLevel1 ?? true,
          enableLevel2: options?.enableLevel2 ?? true,
          enableLevel3: options?.enableLevel3 ?? false,
          timeout: options?.timeout ?? 300000,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return data.data;
      }

      throw new Error(data.error || 'Failed to start privacy scan');
    } catch (error) {
      console.error('Error starting privacy scan:', error);
      throw new Error('Failed to start privacy scan');
    }
  }

  /**
   * Start a new HIPAA security scan
   */
  async startSecurityScan(
    targetUrl: string,
    options?: {
      maxPages?: number;
      scanDepth?: number;
      timeout?: number;
      enableVulnerabilityScanning?: boolean;
      enableSSLAnalysis?: boolean;
      enableContentAnalysis?: boolean;
    },
  ): Promise<HipaaSecurityScanResult> {
    try {
      const response = await fetch(`${this.baseUrl}/compliance/hipaa/security/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUrl,
          maxPages: options?.maxPages ?? 15,
          scanDepth: options?.scanDepth ?? 2,
          timeout: options?.timeout ?? 1800000,
          enableVulnerabilityScanning: options?.enableVulnerabilityScanning ?? true,
          enableSSLAnalysis: options?.enableSSLAnalysis ?? true,
          enableContentAnalysis: options?.enableContentAnalysis ?? true,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      if (data.success && data.data) {
        return data.data.result || data.data;
      }

      throw new Error(data.error || 'Failed to start security scan');
    } catch (error) {
      console.error('Error starting security scan:', error);
      throw new Error('Failed to start security scan');
    }
  }

  /**
   * Get dashboard metrics for analytics
   */
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const [privacyScans, securityScans] = await Promise.all([
      this.getPrivacyScans(),
      this.getSecurityScans(),
    ]);

    const allScans = [...privacyScans, ...securityScans];
    const totalScans = allScans.length;

    if (totalScans === 0) {
      return {
        totalScans: 0,
        averageScore: 0,
        riskDistribution: { critical: 0, high: 0, medium: 0, low: 0 },
        complianceRate: 0,
        trendsData: [],
      };
    }

    const scores = allScans.map((scan) => this.getScore(scan));
    const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);

    // Calculate risk distribution
    const riskDistribution = { critical: 0, high: 0, medium: 0, low: 0 };
    scores.forEach((score) => {
      const risk = this.getRiskLevelFromScore(score);
      riskDistribution[risk]++;
    });

    // Calculate compliance rate (percentage of scans >= 85%)
    const compliantScans = scores.filter((score) => score >= 85).length;
    const complianceRate = Math.round((compliantScans / totalScans) * 100);

    return {
      totalScans,
      averageScore,
      riskDistribution,
      complianceRate,
      trendsData: [], // TODO: Implement trends calculation
    };
  }

  /**
   * Get default dashboard data when API calls fail
   */
  private getDefaultDashboardData(): HipaaDashboardData {
    return {
      overview: {
        overallScore: 0,
        riskLevel: 'medium',
        complianceStatus: 'non_compliant',
        lastScanDate: new Date().toISOString(),
        totalScans: 0,
      },
      privacyModule: {
        scanCount: 0,
        status: 'not_scanned',
        recentScans: [],
      },
      securityModule: {
        scanCount: 0,
        status: 'not_scanned',
        recentScans: [],
      },
      recentActivity: [],
    };
  }
}

// Export singleton instance
export const hipaaDashboardService = new HipaaDashboardService();
