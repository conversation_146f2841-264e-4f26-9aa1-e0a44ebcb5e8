/**
 * End-to-End Tests for HIPAA Dashboard
 * Tests complete user workflows in a real browser environment
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.E2E_BASE_URL || 'http://localhost:3000';
const DASHBOARD_URL = `${BASE_URL}/dashboard/hipaa`;

// Helper functions
async function waitForDashboardLoad(page: Page) {
  await page.waitForSelector('[data-testid="hipaa-dashboard"]', { timeout: 10000 });
  await page.waitForLoadState('networkidle');
}

async function mockApiResponses(page: Page) {
  // Mock the dashboard API endpoint
  await page.route('/api/v1/compliance/hipaa/dashboard', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        data: {
          overview: {
            overallScore: 85,
            riskLevel: 'medium',
            complianceStatus: 'partially_compliant',
            lastScanDate: '2025-06-24T10:30:00Z',
            totalScans: 24
          },
          privacyModule: {
            latestScore: 82,
            scanCount: 12,
            lastScanDate: '2025-06-24T10:30:00Z',
            status: 'active',
            recentScans: []
          },
          securityModule: {
            latestScore: 88,
            scanCount: 12,
            lastScanDate: '2025-06-23T14:15:00Z',
            status: 'active',
            recentScans: []
          },
          recentActivity: [
            {
              id: 'activity-1',
              type: 'privacy',
              url: 'https://example.com',
              timestamp: '2025-06-24T10:30:00Z',
              score: 82,
              status: 'completed',
              riskLevel: 'medium'
            }
          ]
        }
      })
    });
  });
}

test.describe('HIPAA Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await mockApiResponses(page);
  });

  test.describe('Page Load and Initial State', () => {
    test('should load dashboard page successfully', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check page title
      await expect(page).toHaveTitle(/HIPAA Compliance Dashboard/);

      // Check main heading
      await expect(page.getByRole('heading', { level: 1 })).toContainText('HIPAA Compliance Dashboard');

      // Check that main sections are visible
      await expect(page.getByText('Comprehensive view of your HIPAA privacy and security compliance')).toBeVisible();
    });

    test('should display compliance scores correctly', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check overall score
      await expect(page.getByText('85')).toBeVisible();

      // Check risk level
      await expect(page.getByText('Medium Risk')).toBeVisible();

      // Check module scores
      await expect(page.getByText('82')).toBeVisible(); // Privacy score
      await expect(page.getByText('88')).toBeVisible(); // Security score
    });

    test('should display recent activity', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      await expect(page.getByText('Recent Scan Activity')).toBeVisible();
      await expect(page.getByText('https://example.com')).toBeVisible();
      await expect(page.getByText('Privacy Policy Scan')).toBeVisible();
    });
  });

  test.describe('User Interactions', () => {
    test('should refresh dashboard when refresh button is clicked', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click refresh button
      const refreshButton = page.getByRole('button', { name: /refresh dashboard data/i });
      await refreshButton.click();

      // Should show loading state
      await expect(page.getByText('Refreshing...')).toBeVisible();

      // Wait for refresh to complete
      await page.waitForLoadState('networkidle');
      await expect(page.getByText('Refresh')).toBeVisible();
    });

    test('should navigate to privacy module', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click on privacy module card or button
      const privacyButton = page.getByRole('button', { name: /view privacy results/i });
      await privacyButton.click();

      // Should navigate to privacy page
      await expect(page).toHaveURL(/\/dashboard\/hipaa\/privacy/);
    });

    test('should navigate to security module', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click on security module card or button
      const securityButton = page.getByRole('button', { name: /view security results/i });
      await securityButton.click();

      // Should navigate to security page
      await expect(page).toHaveURL(/\/dashboard\/hipaa\/security/);
    });

    test('should start new privacy scan', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Click start privacy scan button
      const startScanButton = page.getByRole('button', { name: /start privacy scan/i });
      await startScanButton.click();

      // Should show scan initiation (this would depend on actual implementation)
      // For now, just verify the button was clickable
      await expect(startScanButton).toBeVisible();
    });
  });

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check that content is still visible and accessible
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByText('85')).toBeVisible();

      // Check that buttons are properly sized for touch
      const refreshButton = page.getByRole('button', { name: /refresh dashboard data/i });
      const buttonBox = await refreshButton.boundingBox();
      expect(buttonBox?.height).toBeGreaterThan(44); // Minimum touch target size
    });

    test('should work on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 }); // iPad
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check that layout adapts properly
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByText('85')).toBeVisible();
    });

    test('should work on desktop', async ({ page }) => {
      await page.setViewportSize({ width: 1920, height: 1080 }); // Desktop
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check that all elements are properly laid out
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByText('85')).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should be navigable with keyboard', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Tab through interactive elements
      await page.keyboard.press('Tab');
      await expect(page.getByRole('button', { name: /refresh dashboard data/i })).toBeFocused();

      await page.keyboard.press('Tab');
      await expect(page.getByRole('link', { name: /view detailed compliance reports/i })).toBeFocused();

      // Test Enter key activation
      await page.keyboard.press('Enter');
      // Should navigate or trigger action
    });

    test('should have proper ARIA labels', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check for main landmark
      await expect(page.getByRole('main')).toBeVisible();

      // Check for proper heading structure
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();

      // Check for button labels
      await expect(page.getByRole('button', { name: /refresh dashboard data/i })).toBeVisible();
    });

    test('should work with screen readers', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Check that important content has proper semantic markup
      const mainContent = page.getByRole('main');
      await expect(mainContent).toHaveAttribute('aria-label', 'HIPAA Compliance Dashboard');

      // Check that scores have proper labels
      const scoreElements = page.getByText('85');
      await expect(scoreElements.first()).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test('should handle API errors gracefully', async ({ page }) => {
      // Mock API error
      await page.route('/api/v1/compliance/hipaa/dashboard', async route => {
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            error: 'Internal server error'
          })
        });
      });

      await page.goto(DASHBOARD_URL);

      // Should show error message
      await expect(page.getByText('Error Loading Dashboard')).toBeVisible();
      await expect(page.getByRole('button', { name: /try again/i })).toBeVisible();
    });

    test('should allow retry after error', async ({ page }) => {
      let callCount = 0;
      await page.route('/api/v1/compliance/hipaa/dashboard', async route => {
        callCount++;
        if (callCount === 1) {
          // First call fails
          await route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({
              success: false,
              error: 'Internal server error'
            })
          });
        } else {
          // Second call succeeds
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              success: true,
              data: {
                overview: {
                  overallScore: 85,
                  riskLevel: 'medium',
                  complianceStatus: 'partially_compliant',
                  lastScanDate: '2025-06-24T10:30:00Z',
                  totalScans: 24
                },
                privacyModule: { latestScore: 82, scanCount: 12, status: 'active', recentScans: [] },
                securityModule: { latestScore: 88, scanCount: 12, status: 'active', recentScans: [] },
                recentActivity: []
              }
            })
          });
        }
      });

      await page.goto(DASHBOARD_URL);

      // Should show error initially
      await expect(page.getByText('Error Loading Dashboard')).toBeVisible();

      // Click retry
      await page.getByRole('button', { name: /try again/i }).click();

      // Should load successfully
      await waitForDashboardLoad(page);
      await expect(page.getByRole('heading', { level: 1 })).toContainText('HIPAA Compliance Dashboard');
    });
  });

  test.describe('Performance', () => {
    test('should load within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(5000); // Should load within 5 seconds
    });

    test('should not have memory leaks', async ({ page }) => {
      await page.goto(DASHBOARD_URL);
      await waitForDashboardLoad(page);

      // Refresh multiple times to check for memory leaks
      for (let i = 0; i < 5; i++) {
        await page.reload();
        await waitForDashboardLoad(page);
      }

      // If we get here without timeout, no major memory leaks
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    });
  });
});
