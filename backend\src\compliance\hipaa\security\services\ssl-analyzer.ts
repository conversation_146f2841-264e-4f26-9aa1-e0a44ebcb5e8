import ssl<PERSON>hecker from 'ssl-checker';
import { connect, TLSSocket } from 'tls';
import * as https from 'https';
import { SSLAnalysisResult, SSLVulnerability } from '../types';

export class SSLAnalyzer {
  async analyzeDomain(hostname: string, port: number = 443): Promise<SSLAnalysisResult> {
    try {
      console.log(`🔒 Starting SSL analysis for ${hostname}:${port}`);

      // Basic SSL certificate check with timeout and error handling
      let certInfo: {
        valid: boolean;
        daysRemaining?: number;
        issuer?: string;
        subject?: string;
      };
      try {
        certInfo = await Promise.race([
          sslChecker(hostname, { method: 'GET', port }) as Promise<{
            valid: boolean;
            daysRemaining?: number;
            issuer?: string;
            subject?: string;
          }>,
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('SSL checker timeout')), 10000),
          ),
        ]);
        console.log(`✅ SSL certificate check completed for ${hostname}`);
      } catch (certError) {
        console.warn(
          `⚠️ SSL certificate check failed for ${hostname}:`,
          certError instanceof Error ? certError.message : 'Unknown error',
        );
        // Provide fallback certificate info
        certInfo = {
          valid: false,
          daysRemaining: 0,
          issuer: 'Unknown - SSL check failed',
          subject: hostname,
        };
      }

      // Advanced TLS configuration check with error handling
      let tlsInfo: {
        version: string;
        cipher: string;
        keyExchange: string;
        serverSignature: string;
        supportedProtocols: string[];
      };
      try {
        tlsInfo = await this.checkTLSConfiguration(hostname, port);
        console.log(`✅ TLS configuration check completed for ${hostname}`);
      } catch (tlsError) {
        console.warn(
          `⚠️ TLS configuration check failed for ${hostname}:`,
          tlsError instanceof Error ? tlsError.message : 'Unknown error',
        );
        // Provide fallback TLS info
        tlsInfo = {
          version: 'unknown',
          cipher: 'unknown',
          keyExchange: 'unknown',
          serverSignature: 'unknown',
          supportedProtocols: ['unknown'],
        };
      }

      // Vulnerability assessment
      const vulnerabilities = await this.assessVulnerabilities(hostname, port, tlsInfo);

      // HIPAA compliance check
      const hipaaCompliant = this.assessHIPAACompliance(certInfo, tlsInfo, vulnerabilities);

      // Calculate grade
      const grade = this.calculateSSLGrade(certInfo, tlsInfo, vulnerabilities);

      console.log(
        `🔒 SSL analysis completed for ${hostname} - Grade: ${grade}, HIPAA Compliant: ${hipaaCompliant}`,
      );

      return {
        isValid: certInfo.valid,
        daysRemaining: certInfo.daysRemaining || 0,
        issuer: (certInfo as { issuer?: string }).issuer || 'Unknown',
        subject: (certInfo as { subject?: string }).subject || hostname,
        tlsVersion: tlsInfo.version,
        cipherSuite: tlsInfo.cipher,
        keyExchange: tlsInfo.keyExchange,
        serverSignature: tlsInfo.serverSignature,
        hipaaCompliant,
        vulnerabilities,
        grade,
      };
    } catch (error) {
      console.error(
        `❌ SSL analysis failed for ${hostname}:`,
        error instanceof Error ? error.message : 'Unknown error',
      );
      throw new Error(
        `SSL analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  private async checkTLSConfiguration(
    hostname: string,
    port: number,
  ): Promise<{
    version: string;
    cipher: string;
    keyExchange: string;
    serverSignature: string;
    supportedProtocols: string[];
  }> {
    // Try multiple approaches with different TLS configurations
    const tlsConfigs = [
      { secureProtocol: 'TLSv1_3_method', name: 'TLS 1.3' },
      { secureProtocol: 'TLSv1_2_method', name: 'TLS 1.2' },
      { secureProtocol: 'TLS_method', name: 'Auto TLS' },
    ];

    let lastError: Error | null = null;

    for (const config of tlsConfigs) {
      try {
        console.log(`🔐 Trying ${config.name} connection to ${hostname}:${port}`);
        const result = await this.attemptTLSConnection(hostname, port, config.secureProtocol);
        console.log(`✅ ${config.name} connection successful for ${hostname}`);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.warn(`⚠️ ${config.name} failed for ${hostname}:`, lastError.message);

        // Add delay between attempts to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // If all TLS methods fail, try alternative approaches
    try {
      console.log(`🔄 Attempting alternative TLS analysis for ${hostname}`);
      return await this.alternativeTLSCheck(hostname, port);
    } catch (altError) {
      console.warn(
        `⚠️ Alternative TLS check also failed for ${hostname}:`,
        altError instanceof Error ? altError.message : 'Unknown error',
      );

      // Return graceful fallback instead of throwing
      console.log(`🔄 Using fallback TLS info for ${hostname} due to connection issues`);
      return {
        version: 'connection_blocked',
        cipher: 'unable_to_determine',
        keyExchange: 'connection_failed',
        serverSignature: 'blocked_by_server',
        supportedProtocols: ['connection_blocked'],
      };
    }
  }

  private async attemptTLSConnection(
    hostname: string,
    port: number,
    secureProtocol: string,
  ): Promise<{
    version: string;
    cipher: string;
    keyExchange: string;
    serverSignature: string;
    supportedProtocols: string[];
  }> {
    return new Promise((resolve, reject) => {
      const socket = connect({
        host: hostname,
        port,
        secureProtocol,
        timeout: 6000, // Reduced timeout
        rejectUnauthorized: false, // Allow self-signed certs for analysis
        servername: hostname, // SNI support
      });

      let resolved = false;
      const timeoutId = setTimeout(() => {
        if (resolved) return;
        resolved = true;
        socket.destroy();
        reject(new Error('TLS connection timeout'));
      }, 6000);

      socket.on('secureConnect', () => {
        if (resolved) return;
        resolved = true;
        clearTimeout(timeoutId);

        try {
          const cipher = socket.getCipher();
          const protocol = socket.getProtocol();
          const peerCertificate = socket.getPeerCertificate();

          resolve({
            version: protocol || 'unknown',
            cipher: cipher?.name || 'unknown',
            keyExchange: cipher?.version || 'unknown',
            serverSignature: peerCertificate?.fingerprint || 'unknown',
            supportedProtocols: [protocol || 'unknown'],
          });
        } catch (error) {
          resolve({
            version: 'extraction_failed',
            cipher: 'extraction_failed',
            keyExchange: 'extraction_failed',
            serverSignature: 'extraction_failed',
            supportedProtocols: ['extraction_failed'],
          });
        } finally {
          socket.destroy();
        }
      });

      socket.on('error', (error: Error) => {
        if (resolved) return;
        resolved = true;
        clearTimeout(timeoutId);
        socket.destroy();
        reject(error);
      });
    });
  }

  private async alternativeTLSCheck(
    hostname: string,
    port: number,
  ): Promise<{
    version: string;
    cipher: string;
    keyExchange: string;
    serverSignature: string;
    supportedProtocols: string[];
  }> {
    // Try using a simple HTTPS request to gather basic TLS info
    try {
      const https = await import('https');
      const url = `https://${hostname}:${port}/`;

      return new Promise((resolve, reject) => {
        const req = https.get(
          url,
          {
            timeout: 5000,
            rejectUnauthorized: false,
          },
          (res) => {
            const socket = res.socket as TLSSocket;
            if (socket && typeof socket.getCipher === 'function') {
              const cipher = socket.getCipher();
              const protocol = socket.getProtocol();

              resolve({
                version: protocol || 'https_detected',
                cipher: cipher?.name || 'https_cipher',
                keyExchange: cipher?.version || 'https_exchange',
                serverSignature: 'https_connection',
                supportedProtocols: [protocol || 'https'],
              });
            } else {
              resolve({
                version: 'https_available',
                cipher: 'standard_https',
                keyExchange: 'standard',
                serverSignature: 'https_verified',
                supportedProtocols: ['https'],
              });
            }
          },
        );

        req.on('error', (error) => {
          reject(error);
        });

        req.setTimeout(5000, () => {
          req.destroy();
          reject(new Error('Alternative TLS check timeout'));
        });
      });
    } catch (error) {
      throw new Error(
        `Alternative TLS check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  private async assessVulnerabilities(
    hostname: string,
    _port: number,
    tlsInfo: { version: string; cipher: string },
  ): Promise<SSLVulnerability[]> {
    const vulnerabilities: SSLVulnerability[] = [];

    // Handle connection-blocked scenarios
    if (tlsInfo.version === 'connection_blocked') {
      vulnerabilities.push({
        type: 'connection_blocked',
        severity: 'medium',
        description: `Server ${hostname} blocks automated security analysis connections`,
        remediation:
          'Manual verification required for complete security assessment. Consider whitelisting security scanners.',
      });
      return vulnerabilities;
    }

    // Handle extraction failures
    if (tlsInfo.version === 'extraction_failed') {
      vulnerabilities.push({
        type: 'analysis_limited',
        severity: 'low',
        description: 'TLS configuration analysis was limited due to connection constraints',
        remediation: 'Verify TLS configuration manually or adjust firewall settings',
      });
    }

    // Check for weak TLS versions
    if (tlsInfo.version.includes('1.0') || tlsInfo.version.includes('1.1')) {
      vulnerabilities.push({
        type: 'weak_tls_version',
        severity: 'high',
        description: `Weak TLS version detected: ${tlsInfo.version}`,
        remediation: 'Upgrade to TLS 1.2 or higher',
      });
    }

    // Check for weak ciphers
    const weakCiphers = ['RC4', 'DES', '3DES', 'MD5'];
    if (weakCiphers.some((weak) => tlsInfo.cipher.includes(weak))) {
      vulnerabilities.push({
        type: 'weak_cipher',
        severity: 'medium',
        description: `Weak cipher suite detected: ${tlsInfo.cipher}`,
        remediation: 'Configure strong cipher suites (AES-256, ChaCha20)',
      });
    }

    // Check for unknown/unanalyzable configurations
    if (tlsInfo.version === 'unknown' && tlsInfo.cipher === 'unknown') {
      vulnerabilities.push({
        type: 'configuration_unknown',
        severity: 'medium',
        description: 'Unable to determine TLS configuration details',
        remediation: 'Verify server accessibility and TLS configuration',
      });
    }

    return vulnerabilities;
  }

  private assessHIPAACompliance(
    certInfo: { valid: boolean; daysRemaining?: number },
    tlsInfo: { version: string; cipher: string },
    vulnerabilities: SSLVulnerability[],
  ): boolean {
    // Handle connection-blocked scenarios - cannot determine compliance
    if (tlsInfo.version === 'connection_blocked' || tlsInfo.version === 'extraction_failed') {
      return false; // Cannot verify HIPAA compliance without proper analysis
    }

    // Handle alternative TLS checks - basic HTTPS is a good sign but not sufficient for HIPAA
    if (tlsInfo.version === 'https_available' || tlsInfo.version === 'https_detected') {
      // Basic HTTPS is present, but we need more details for full HIPAA compliance
      return certInfo.valid && (certInfo.daysRemaining || 0) >= 30;
    }

    // HIPAA requires valid certificates
    if (!certInfo.valid) return false;

    // Certificate should not expire soon
    if ((certInfo.daysRemaining || 0) < 30) return false;

    // Must use TLS 1.2 or higher
    if (!tlsInfo.version.includes('1.2') && !tlsInfo.version.includes('1.3')) {
      // Allow some flexibility for alternative detection methods
      if (!tlsInfo.version.includes('https') && tlsInfo.version !== 'unknown') {
        return false;
      }
    }

    // No critical or high vulnerabilities
    const criticalVulns = vulnerabilities.filter(
      (v) => v.severity === 'critical' || v.severity === 'high',
    );
    if (criticalVulns.length > 0) return false;

    // Must use strong encryption (with some flexibility for detection limitations)
    if (
      tlsInfo.cipher !== 'unknown' &&
      tlsInfo.cipher !== 'https_cipher' &&
      tlsInfo.cipher !== 'standard_https' &&
      !tlsInfo.cipher.includes('AES') &&
      !tlsInfo.cipher.includes('ChaCha20')
    ) {
      return false;
    }

    return true;
  }

  private calculateSSLGrade(
    certInfo: { valid: boolean; daysRemaining?: number },
    tlsInfo: { version: string; cipher: string },
    vulnerabilities: SSLVulnerability[],
  ): string {
    // Handle connection-blocked scenarios
    if (tlsInfo.version === 'connection_blocked') {
      return 'T'; // T for "Trust" issues / Cannot be tested
    }

    // Handle extraction failures
    if (tlsInfo.version === 'extraction_failed') {
      return 'M'; // M for "Misconfigured" or analysis issues
    }

    let score = 100;

    // Deduct for invalid certificate
    if (!certInfo.valid) score -= 50;

    // Deduct for expiring certificate
    if ((certInfo.daysRemaining || 0) < 30) score -= 20;

    // Deduct for weak TLS version
    if (tlsInfo.version.includes('1.0') || tlsInfo.version.includes('1.1')) score -= 30;

    // Handle alternative detection methods with moderate scoring
    if (tlsInfo.version === 'https_available' || tlsInfo.version === 'https_detected') {
      // Basic HTTPS detected but limited analysis - moderate score
      score = Math.min(score, 75);
    }

    // Handle unknown configurations
    if (tlsInfo.version === 'unknown' && tlsInfo.cipher === 'unknown') {
      score -= 25; // Significant deduction for unknown config
    }

    // Deduct for vulnerabilities
    vulnerabilities.forEach((vuln) => {
      switch (vuln.severity) {
        case 'critical':
          score -= 40;
          break;
        case 'high':
          score -= 20;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    // Convert score to grade
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }
}
