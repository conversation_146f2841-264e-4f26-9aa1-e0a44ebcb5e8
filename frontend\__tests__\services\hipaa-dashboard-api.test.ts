/**
 * Unit Tests for HIPAA Dashboard API Service
 * Tests all API methods, data aggregation, and error handling
 */

import { hipaaDashboardService } from '@/services/hipaa-dashboard-api';
import { HipaaPrivacyScanResult } from '@/types/hipaa-privacy';
import { HipaaSecurityScanResult } from '@/types/hipaa-security';

// Mock fetch globally
global.fetch = jest.fn();

describe('HipaaDashboardService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('getDashboardData', () => {
    it('should fetch dashboard data from backend endpoint', async () => {
      const mockDashboardData = {
        overview: {
          overallScore: 85,
          riskLevel: 'medium',
          complianceStatus: 'partially_compliant',
          lastScanDate: '2025-06-24T10:30:00Z',
          totalScans: 24
        },
        privacyModule: {
          latestScore: 82,
          scanCount: 12,
          lastScanDate: '2025-06-24T10:30:00Z',
          status: 'active',
          recentScans: []
        },
        securityModule: {
          latestScore: 88,
          scanCount: 12,
          lastScanDate: '2025-06-23T14:15:00Z',
          status: 'active',
          recentScans: []
        },
        recentActivity: []
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockDashboardData })
      });

      const result = await hipaaDashboardService.getDashboardData();

      expect(fetch).toHaveBeenCalledWith('/api/v1/compliance/hipaa/dashboard');
      expect(result).toEqual(mockDashboardData);
    });

    it('should fallback to individual module calls when backend endpoint fails', async () => {
      // Mock backend endpoint failure
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        statusText: 'Internal Server Error'
      });

      // Mock individual module calls
      const mockPrivacyScans: HipaaPrivacyScanResult[] = [
        {
          targetUrl: 'https://example.com',
          timestamp: '2025-06-24T10:30:00Z',
          overallScore: 82,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 20,
            failedChecks: 4,
            criticalIssues: 1,
            highIssues: 2,
            mediumIssues: 1,
            lowIssues: 0,
            overallScore: 82,
            complianceLevel: 'mostly_compliant',
            riskLevel: 'medium',
            analysisLevelsUsed: [1, 2, 3]
          },
          checks: [],
          recommendations: [],
          metadata: {
            scanDuration: 45000,
            pagesAnalyzed: 5,
            privacyPolicyFound: true,
            privacyPolicyUrl: 'https://example.com/privacy',
            lastUpdated: '2025-06-24T10:30:00Z',
            scanVersion: '1.0.0'
          }
        }
      ];

      const mockSecurityScans: HipaaSecurityScanResult[] = [
        {
          scanId: 'sec-001',
          targetUrl: 'https://example.com',
          scanTimestamp: new Date('2025-06-24T11:00:00Z'),
          overallScore: 88,
          riskLevel: 'low',
          passedTests: [],
          failedTests: [],
          vulnerabilities: [],
          categoryResults: [],
          scanDuration: 120000,
          toolsUsed: ['Nuclei'],
          pagesScanned: ['https://example.com'],
          scanConfig: {
            targetUrl: 'https://example.com',
            timeout: 300000,
            maxPages: 15,
            scanDepth: 2,
            enableVulnerabilityScanning: true,
            enableSSLAnalysis: true,
            enableContentAnalysis: true
          }
        }
      ];

      // Mock subsequent calls for privacy and security scans
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: mockPrivacyScans })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: mockSecurityScans })
        });

      const result = await hipaaDashboardService.getDashboardData();

      expect(result).toBeDefined();
      expect(result.overview).toBeDefined();
      expect(result.privacyModule).toBeDefined();
      expect(result.securityModule).toBeDefined();
      expect(result.recentActivity).toBeDefined();
    });

    it('should handle complete API failure gracefully', async () => {
      (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      await expect(hipaaDashboardService.getDashboardData()).rejects.toThrow('Failed to load dashboard data');
    });
  });

  describe('calculateOverallScore', () => {
    it('should calculate weighted score correctly', () => {
      const privacyScans: HipaaPrivacyScanResult[] = [
        {
          targetUrl: 'https://example.com',
          timestamp: '2025-06-24T10:30:00Z',
          overallScore: 80,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 19,
            failedChecks: 5,
            criticalIssues: 0,
            highIssues: 2,
            mediumIssues: 3,
            lowIssues: 0,
            overallScore: 80,
            complianceLevel: 'mostly_compliant',
            riskLevel: 'medium',
            analysisLevelsUsed: [1, 2, 3]
          },
          checks: [],
          recommendations: [],
          metadata: {
            scanDuration: 45000,
            pagesAnalyzed: 5,
            privacyPolicyFound: true,
            privacyPolicyUrl: 'https://example.com/privacy',
            lastUpdated: '2025-06-24T10:30:00Z',
            scanVersion: '1.0.0'
          }
        }
      ];

      const securityScans: HipaaSecurityScanResult[] = [
        {
          scanId: 'sec-001',
          targetUrl: 'https://example.com',
          scanTimestamp: new Date('2025-06-24T11:00:00Z'),
          overallScore: 90,
          riskLevel: 'low',
          passedTests: [],
          failedTests: [],
          vulnerabilities: [],
          categoryResults: [],
          scanDuration: 120000,
          toolsUsed: ['Nuclei'],
          pagesScanned: ['https://example.com'],
          scanConfig: {
            targetUrl: 'https://example.com',
            timeout: 300000,
            maxPages: 15,
            scanDepth: 2,
            enableVulnerabilityScanning: true,
            enableSSLAnalysis: true,
            enableContentAnalysis: true
          }
        }
      ];

      const result = hipaaDashboardService.calculateOverallScore(privacyScans, securityScans);
      
      // Expected: (80 * 0.4) + (90 * 0.6) = 32 + 54 = 86
      expect(result).toBe(86);
    });

    it('should handle empty scan arrays', () => {
      const result = hipaaDashboardService.calculateOverallScore([], []);
      expect(result).toBe(0);
    });
  });

  describe('getRiskLevelFromScore', () => {
    it('should return correct risk levels for different scores', () => {
      expect(hipaaDashboardService.getRiskLevelFromScore(95)).toBe('low');
      expect(hipaaDashboardService.getRiskLevelFromScore(85)).toBe('medium');
      expect(hipaaDashboardService.getRiskLevelFromScore(70)).toBe('high');
      expect(hipaaDashboardService.getRiskLevelFromScore(50)).toBe('critical');
    });
  });

  describe('getComplianceStatus', () => {
    it('should return correct compliance status for different scores', () => {
      expect(hipaaDashboardService.getComplianceStatus(90)).toBe('compliant');
      expect(hipaaDashboardService.getComplianceStatus(75)).toBe('partially_compliant');
      expect(hipaaDashboardService.getComplianceStatus(50)).toBe('non_compliant');
    });
  });

  describe('getPrivacyScans', () => {
    it('should fetch privacy scans from new endpoint first', async () => {
      const mockScans = [
        {
          targetUrl: 'https://example.com',
          timestamp: '2025-06-24T10:30:00Z',
          overallScore: 82,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 20,
            failedChecks: 4,
            criticalIssues: 1,
            highIssues: 2,
            mediumIssues: 1,
            lowIssues: 0,
            overallScore: 82,
            complianceLevel: 'mostly_compliant',
            riskLevel: 'medium',
            analysisLevelsUsed: [1, 2, 3]
          },
          checks: [],
          recommendations: [],
          metadata: {
            scanDuration: 45000,
            pagesAnalyzed: 5,
            privacyPolicyFound: true,
            privacyPolicyUrl: 'https://example.com/privacy',
            lastUpdated: '2025-06-24T10:30:00Z',
            scanVersion: '1.0.0'
          }
        }
      ];

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockScans })
      });

      const result = await hipaaDashboardService.getPrivacyScans(10);

      expect(fetch).toHaveBeenCalledWith('/api/v1/compliance/hipaa/privacy/scans?limit=10');
      expect(result).toEqual(mockScans);
    });

    it('should fallback to original endpoint if new endpoint fails', async () => {
      const mockScans = [
        {
          targetUrl: 'https://example.com',
          timestamp: '2025-06-24T10:30:00Z',
          overallScore: 82,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 20,
            failedChecks: 4,
            criticalIssues: 1,
            highIssues: 2,
            mediumIssues: 1,
            lowIssues: 0,
            overallScore: 82,
            complianceLevel: 'mostly_compliant',
            riskLevel: 'medium',
            analysisLevelsUsed: [1, 2, 3]
          },
          checks: [],
          recommendations: [],
          metadata: {
            scanDuration: 45000,
            pagesAnalyzed: 5,
            privacyPolicyFound: true,
            privacyPolicyUrl: 'https://example.com/privacy',
            lastUpdated: '2025-06-24T10:30:00Z',
            scanVersion: '1.0.0'
          }
        }
      ];

      // First call fails
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      });

      // Second call succeeds
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockScans })
      });

      const result = await hipaaDashboardService.getPrivacyScans(10);

      expect(fetch).toHaveBeenCalledTimes(2);
      expect(fetch).toHaveBeenNthCalledWith(1, '/api/v1/compliance/hipaa/privacy/scans?limit=10');
      expect(fetch).toHaveBeenNthCalledWith(2, '/api/v1/hipaa-privacy/scans?limit=10');
      expect(result).toEqual(mockScans);
    });

    it('should return mock data if both endpoints fail', async () => {
      (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await hipaaDashboardService.getPrivacyScans(10);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('getDashboardMetrics', () => {
    it('should calculate metrics correctly', async () => {
      // Mock the getPrivacyScans and getSecurityScans methods
      jest.spyOn(hipaaDashboardService, 'getPrivacyScans').mockResolvedValue([
        {
          targetUrl: 'https://example.com',
          timestamp: '2025-06-24T10:30:00Z',
          overallScore: 80,
          overallPassed: true,
          summary: {
            totalChecks: 24,
            passedChecks: 19,
            failedChecks: 5,
            criticalIssues: 0,
            highIssues: 2,
            mediumIssues: 3,
            lowIssues: 0,
            overallScore: 80,
            complianceLevel: 'mostly_compliant',
            riskLevel: 'medium',
            analysisLevelsUsed: [1, 2, 3]
          },
          checks: [],
          recommendations: [],
          metadata: {
            scanDuration: 45000,
            pagesAnalyzed: 5,
            privacyPolicyFound: true,
            privacyPolicyUrl: 'https://example.com/privacy',
            lastUpdated: '2025-06-24T10:30:00Z',
            scanVersion: '1.0.0'
          }
        }
      ]);

      jest.spyOn(hipaaDashboardService, 'getSecurityScans').mockResolvedValue([
        {
          scanId: 'sec-001',
          targetUrl: 'https://example.com',
          scanTimestamp: new Date('2025-06-24T11:00:00Z'),
          overallScore: 90,
          riskLevel: 'low',
          passedTests: [],
          failedTests: [],
          vulnerabilities: [],
          categoryResults: [],
          scanDuration: 120000,
          toolsUsed: ['Nuclei'],
          pagesScanned: ['https://example.com'],
          scanConfig: {
            targetUrl: 'https://example.com',
            timeout: 300000,
            maxPages: 15,
            scanDepth: 2,
            enableVulnerabilityScanning: true,
            enableSSLAnalysis: true,
            enableContentAnalysis: true
          }
        }
      ]);

      const result = await hipaaDashboardService.getDashboardMetrics();

      expect(result.totalScans).toBe(2);
      expect(result.averageScore).toBe(85); // (80 + 90) / 2
      expect(result.riskDistribution).toBeDefined();
      expect(result.complianceRate).toBeDefined();
    });

    it('should handle empty scan data', async () => {
      jest.spyOn(hipaaDashboardService, 'getPrivacyScans').mockResolvedValue([]);
      jest.spyOn(hipaaDashboardService, 'getSecurityScans').mockResolvedValue([]);

      const result = await hipaaDashboardService.getDashboardMetrics();

      expect(result.totalScans).toBe(0);
      expect(result.averageScore).toBe(0);
      expect(result.complianceRate).toBe(0);
    });
  });
});
