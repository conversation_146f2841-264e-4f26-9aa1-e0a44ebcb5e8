/**
 * Animation and Transition System
 * Provides smooth, accessible animations with reduced motion support
 */

/* ===== ANIMATION VARIABLES ===== */
:root {
  /* Duration */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
  
  /* Easing */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Transform origins */
  --origin-center: center;
  --origin-top: top;
  --origin-bottom: bottom;
  --origin-left: left;
  --origin-right: right;
}

/* ===== FADE ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-1rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(1rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===== SCALE ANIMATIONS ===== */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-0.5rem);
  }
  70% {
    transform: translateY(-0.25rem);
  }
  90% {
    transform: translateY(-0.125rem);
  }
}

/* ===== SLIDE ANIMATIONS ===== */
@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes dots {
  0%, 20% {
    color: rgba(0, 0, 0, 0);
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                 0.5em 0 0 rgba(0, 0, 0, 0);
  }
  40% {
    color: black;
    text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                 0.5em 0 0 rgba(0, 0, 0, 0);
  }
  60% {
    text-shadow: 0.25em 0 0 black,
                 0.5em 0 0 rgba(0, 0, 0, 0);
  }
  80%, 100% {
    text-shadow: 0.25em 0 0 black,
                 0.5em 0 0 black;
  }
}

/* ===== UTILITY CLASSES ===== */

/* Fade animations */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.animate-fade-out {
  animation: fadeOut var(--duration-normal) var(--ease-in);
}

.animate-fade-in-up {
  animation: fadeInUp var(--duration-normal) var(--ease-out);
}

.animate-fade-in-down {
  animation: fadeInDown var(--duration-normal) var(--ease-out);
}

.animate-fade-in-left {
  animation: fadeInLeft var(--duration-normal) var(--ease-out);
}

.animate-fade-in-right {
  animation: fadeInRight var(--duration-normal) var(--ease-out);
}

/* Scale animations */
.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out);
}

.animate-scale-out {
  animation: scaleOut var(--duration-normal) var(--ease-in);
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Slide animations */
.animate-slide-in-up {
  animation: slideInUp var(--duration-normal) var(--ease-out);
}

.animate-slide-in-down {
  animation: slideInDown var(--duration-normal) var(--ease-out);
}

.animate-slide-in-left {
  animation: slideInLeft var(--duration-normal) var(--ease-out);
}

.animate-slide-in-right {
  animation: slideInRight var(--duration-normal) var(--ease-out);
}

/* Loading animations */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.animate-progress {
  animation: progress 2s infinite;
}

.animate-dots::after {
  content: '...';
  animation: dots 1.5s infinite;
}

/* ===== TRANSITION UTILITIES ===== */

/* Basic transitions */
.transition-all {
  transition: all var(--duration-normal) var(--ease-in-out);
}

.transition-colors {
  transition: color var(--duration-normal) var(--ease-in-out),
              background-color var(--duration-normal) var(--ease-in-out),
              border-color var(--duration-normal) var(--ease-in-out);
}

.transition-opacity {
  transition: opacity var(--duration-normal) var(--ease-in-out);
}

.transition-transform {
  transition: transform var(--duration-normal) var(--ease-in-out);
}

.transition-shadow {
  transition: box-shadow var(--duration-normal) var(--ease-in-out);
}

/* Duration modifiers */
.transition-fast {
  transition-duration: var(--duration-fast);
}

.transition-slow {
  transition-duration: var(--duration-slow);
}

.transition-slower {
  transition-duration: var(--duration-slower);
}

/* Easing modifiers */
.ease-in {
  transition-timing-function: var(--ease-in);
}

.ease-out {
  transition-timing-function: var(--ease-out);
}

.ease-in-out {
  transition-timing-function: var(--ease-in-out);
}

.ease-bounce {
  transition-timing-function: var(--ease-bounce);
}

/* ===== HOVER EFFECTS ===== */
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.hover-fade {
  transition: opacity var(--duration-normal) var(--ease-out);
}

.hover-fade:hover {
  opacity: 0.8;
}

/* ===== FOCUS EFFECTS ===== */
.focus-ring {
  transition: box-shadow var(--duration-fast) var(--ease-out);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.focus-scale {
  transition: transform var(--duration-fast) var(--ease-out);
}

.focus-scale:focus {
  transform: scale(1.02);
}

/* ===== STAGGER ANIMATIONS ===== */
.stagger-children > * {
  animation-delay: calc(var(--stagger-delay, 100ms) * var(--index, 0));
}

.stagger-fade-in > * {
  animation: fadeInUp var(--duration-normal) var(--ease-out) both;
  animation-delay: calc(100ms * var(--index, 0));
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* Keep essential loading indicators */
  .animate-spin,
  .animate-pulse {
    animation: none;
  }
  
  .animate-spin::after {
    content: '⟳';
    display: inline-block;
  }
}

/* ===== DASHBOARD SPECIFIC ANIMATIONS ===== */

/* Card entrance animations */
.dashboard-card-enter {
  animation: fadeInUp var(--duration-normal) var(--ease-out);
}

.dashboard-card-enter:nth-child(1) { animation-delay: 0ms; }
.dashboard-card-enter:nth-child(2) { animation-delay: 100ms; }
.dashboard-card-enter:nth-child(3) { animation-delay: 200ms; }
.dashboard-card-enter:nth-child(4) { animation-delay: 300ms; }

/* Metric counter animation */
@keyframes countUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.metric-counter {
  overflow: hidden;
}

.metric-counter .number {
  display: inline-block;
  animation: countUp var(--duration-slow) var(--ease-out);
}

/* Progress bar animation */
.progress-bar-animated {
  position: relative;
  overflow: hidden;
}

.progress-bar-animated::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progress 2s infinite;
}

/* Status badge animations */
.status-badge-pulse {
  animation: pulse 2s infinite;
}

.status-badge-bounce {
  animation: bounce 1s infinite;
}

/* Loading skeleton shimmer */
.skeleton-shimmer {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
