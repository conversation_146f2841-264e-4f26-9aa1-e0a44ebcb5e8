'use client';

import React from 'react';
import { useTheme } from 'next-themes';

/**
 * DesignSystemWrapper component that ensures consistent styling across light and dark modes
 * while maintaining WCAG AA compliance and design system standards.
 */

interface DesignSystemWrapperProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Wrapper component that applies design system colors consistently
 * across light and dark modes while maintaining WCAG AA compliance.
 */
export function DesignSystemWrapper({ 
  children, 
  className = '', 
  style = {} 
}: DesignSystemWrapperProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const wrapperStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#1E2329' : '#F5F5F5',
    color: isDark ? '#EEEFF1' : '#333333',
    minHeight: '100vh',
    transition: 'background-color 0.2s ease, color 0.2s ease',
    ...style,
  };

  return (
    <div 
      className={`design-system-wrapper ${className}`}
      style={wrapperStyle}
    >
      {children}
    </div>
  );
}

/**
 * Card component that follows design system colors in both light and dark modes
 */
interface DesignSystemCardProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  hover?: boolean;
}

export function DesignSystemCard({ 
  children, 
  className = '', 
  style = {},
  hover = false
}: DesignSystemCardProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const cardStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#252B32' : 'white',
    borderColor: isDark ? '#424952' : '#E5E5E5',
    color: isDark ? '#EEEFF1' : '#333333',
    border: '1px solid',
    borderRadius: '0.5rem',
    transition: 'all 0.2s ease',
    ...style,
  };

  const hoverClass = hover ? (isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-50') : '';

  return (
    <div 
      className={`design-system-card ${hoverClass} ${className}`}
      style={cardStyle}
    >
      {children}
    </div>
  );
}

/**
 * Button component that follows design system colors in both light and dark modes
 */
interface DesignSystemButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export function DesignSystemButton({ 
  children, 
  variant = 'primary',
  size = 'md',
  className = '', 
  style = {},
  onClick,
  disabled = false,
  type = 'button'
}: DesignSystemButtonProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const getButtonStyles = () => {
    const baseStyle: React.CSSProperties = {
      border: 'none',
      borderRadius: '0.375rem',
      fontWeight: '500',
      cursor: disabled ? 'not-allowed' : 'pointer',
      transition: 'all 0.2s ease',
      opacity: disabled ? 0.6 : 1,
      ...style,
    };

    const sizeStyles = {
      sm: { padding: '0.5rem 1rem', fontSize: '0.875rem' },
      md: { padding: '0.75rem 1.5rem', fontSize: '1rem' },
      lg: { padding: '1rem 2rem', fontSize: '1.125rem' },
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          ...sizeStyles[size],
          backgroundColor: isDark ? '#4A9EFF' : '#0055A4',
          color: isDark ? '#1E2329' : 'white',
        };
      case 'secondary':
        return {
          ...baseStyle,
          ...sizeStyles[size],
          backgroundColor: isDark ? '#A855F7' : '#663399',
          color: isDark ? '#1E2329' : 'white',
        };
      case 'outline':
        return {
          ...baseStyle,
          ...sizeStyles[size],
          backgroundColor: 'transparent',
          border: `1px solid ${isDark ? '#4A9EFF' : '#0055A4'}`,
          color: isDark ? '#4A9EFF' : '#0055A4',
        };
      case 'ghost':
        return {
          ...baseStyle,
          ...sizeStyles[size],
          backgroundColor: 'transparent',
          color: isDark ? '#EEEFF1' : '#333333',
        };
      default:
        return { ...baseStyle, ...sizeStyles[size] };
    }
  };

  const getHoverClass = () => {
    if (disabled) return '';
    
    switch (variant) {
      case 'primary':
        return isDark ? 'hover:bg-blue-400' : 'hover:bg-blue-700';
      case 'secondary':
        return isDark ? 'hover:bg-purple-400' : 'hover:bg-purple-700';
      case 'outline':
        return isDark ? 'hover:bg-blue-900' : 'hover:bg-blue-50';
      case 'ghost':
        return isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-100';
      default:
        return '';
    }
  };

  return (
    <button 
      type={type}
      className={`design-system-button ${getHoverClass()} ${className}`}
      style={getButtonStyles()}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}

/**
 * Text component that follows design system typography in both light and dark modes
 */
interface DesignSystemTextProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'tertiary';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  className?: string;
  style?: React.CSSProperties;
  as?: 'p' | 'span' | 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export function DesignSystemText({ 
  children, 
  variant = 'primary',
  size = 'md',
  weight = 'normal',
  className = '', 
  style = {},
  as: Component = 'p'
}: DesignSystemTextProps): JSX.Element {
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  const getTextColor = () => {
    switch (variant) {
      case 'primary':
        return isDark ? '#EEEFF1' : '#333333';
      case 'secondary':
        return isDark ? '#A8ACB0' : '#666666';
      case 'tertiary':
        return isDark ? '#6B7280' : '#999999';
      default:
        return isDark ? '#EEEFF1' : '#333333';
    }
  };

  const getSizeStyles = () => {
    const sizes = {
      xs: { fontSize: '0.75rem', lineHeight: '1rem' },
      sm: { fontSize: '0.875rem', lineHeight: '1.25rem' },
      md: { fontSize: '1rem', lineHeight: '1.5rem' },
      lg: { fontSize: '1.125rem', lineHeight: '1.75rem' },
      xl: { fontSize: '1.25rem', lineHeight: '1.75rem' },
      '2xl': { fontSize: '1.5rem', lineHeight: '2rem' },
      '3xl': { fontSize: '1.875rem', lineHeight: '2.25rem' },
    };
    return sizes[size];
  };

  const getWeightStyles = () => {
    const weights = {
      normal: { fontWeight: '400' },
      medium: { fontWeight: '500' },
      semibold: { fontWeight: '600' },
      bold: { fontWeight: '700' },
    };
    return weights[weight];
  };

  const textStyle: React.CSSProperties = {
    color: getTextColor(),
    transition: 'color 0.2s ease',
    ...getSizeStyles(),
    ...getWeightStyles(),
    ...style,
  };

  return (
    <Component 
      className={`design-system-text ${className}`}
      style={textStyle}
    >
      {children}
    </Component>
  );
}
