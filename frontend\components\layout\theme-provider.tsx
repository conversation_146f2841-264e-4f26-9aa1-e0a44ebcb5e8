'use client';

import * as React from 'react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ThemeProviderProps } from 'next-themes';
import { applyThemeColors, DEFAULT_THEME, type ThemeName } from '@/lib/theme-config';

/**
 * Enhanced ThemeProvider component that enforces design system standards.
 * Uses next-themes with custom theme configuration and Standard theme as default.
 * @param {ThemeProviderProps} props - Props for the NextThemesProvider.
 * @returns {JSX.Element} The ThemeProvider component.
 */
export function ThemeProvider({ children, ...props }: ThemeProviderProps): JSX.Element {
  // Apply theme colors when theme changes
  React.useEffect(() => {
    const handleThemeChange = () => {
      const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'standard';
      applyThemeColors(currentTheme as ThemeName);
    };

    // Apply initial theme
    handleThemeChange();

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          handleThemeChange();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => observer.disconnect();
  }, []);

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={DEFAULT_THEME}
      enableSystem={true}
      disableTransitionOnChange={false}
      themes={['standard', 'dark', 'system']}
      {...props}
    >
      {children}
    </NextThemesProvider>
  );
}
